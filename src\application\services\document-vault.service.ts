import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../../utils/prisma.service';
import { MediaService } from '../../media/media.service';
import { DocumentType } from '@prisma/client';
import {
  IDocumentVault,
  IDocumentVaultService,
} from '../interfaces/application.interfaces';

/**
 * Document Vault Service
 *
 * Core service for document management operations including:
 * - Document upload and storage
 * - Version control and duplicate detection
 * - Document retrieval and filtering
 * - Expiry tracking and management
 * - Integration with workflow system
 */
@Injectable()
export class DocumentVaultService implements IDocumentVaultService {
  private readonly logger = new Logger(DocumentVaultService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mediaService: MediaService,
  ) {}

  /**
   * Upload a document to the vault with duplicate detection
   *
   * @param file - The uploaded file
   * @param metadata - Document metadata
   * @param userId - User ID (optional for guest uploads)
   * @returns Promise<IDocumentVault>
   */
  async uploadDocument(
    file: Express.Multer.File,
    metadata: {
      document_name: string;
      document_type: DocumentType;
      document_category?: string;
      expiry_date?: Date;
      tags?: string[];
      guest_email?: string;
      application_id?: string;
    },
    userId?: string,
  ): Promise<IDocumentVault> {
    try {
      this.logger.log(
        `Uploading document: ${metadata.document_name} for user: ${userId || 'guest'}`,
      );

      // Validate file
      this.validateFile(file);

      // Validate user exists if userId is provided
      let validatedUserId: string | null = null;
      if (userId) {
        const userExists = await this.validateUserExists(userId);
        if (userExists) {
          validatedUserId = userId;
          this.logger.debug(`User validation successful for user: ${userId}`);
        } else {
          this.logger.warn(
            `User ${userId} not found in database. Proceeding with guest upload.`,
          );
          // Don't throw error - gracefully fallback to guest upload
          // This handles cases where JWT is valid but user was deleted
        }
      }

      // Note: Removed duplicate detection as file_hash field was removed for schema simplification

      // Upload file to storage
      this.logger.debug(
        `Attempting to upload file to storage: ${file.originalname}`,
      );
      const uploadResult = await this.mediaService.uploadFile(
        file,
        'documents',
      );

      // Check if upload was successful
      if (uploadResult.status !== 'OK') {
        this.logger.error(`Upload failed with status: ${uploadResult.status}`);
        throw new BadRequestException('Failed to upload file to storage');
      }

      this.logger.debug(`File uploaded successfully to: ${uploadResult.url}`);

      // Create document record
      const documentData: any = {
        document_name: metadata.document_name,
        original_filename: file.originalname,
        document_type: metadata.document_type,
        document_category: metadata.document_category,
        file_path: uploadResult.url,
        file_size: file.size,
        expiry_date: metadata.expiry_date,
        uploaded_by: validatedUserId || userId, // Keep original userId for audit trail
      };

      // Only set user_id if we have a validated user, otherwise leave it null for guest uploads
      if (validatedUserId) {
        documentData.user_id = validatedUserId;
      }

      // Set guest_email if provided or if we're falling back to guest upload
      if (metadata.guest_email || (userId && !validatedUserId)) {
        documentData.guest_email =
          metadata.guest_email || `fallback-${userId}@guest.local`;
      }

      const document = await this.prisma.document_vault.create({
        data: documentData,
      });

      // Note: Removed automatic linking to application to prevent duplicate records
      // The application service will handle linking when uploading application documents
      // This prevents the unique constraint violation on (application_id, document_vault_id)

      this.logger.log(`Document uploaded successfully: ${document.id}`);
      return document as IDocumentVault;
    } catch (error) {
      this.logger.error(
        `Failed to upload document: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check for duplicate documents based on document_name and user identification
   * For authenticated users: document_name + user_id combination
   * For guest users: document_name + email combination (retrieved from payment table)
   */
  private async checkForDuplicateDocument(
    documentName: string,
    applicationId: string,
    userId?: string,
  ): Promise<IDocumentVault | null> {
    try {
      this.logger.debug(
        `Checking for duplicate document: ${documentName} for application: ${applicationId}, user: ${userId || 'guest'}`,
      );

      if (userId) {
        // For authenticated users: check document_name + user_id combination
        const existingDocument = await this.prisma.document_vault.findFirst({
          where: {
            document_name: documentName,
            user_id: userId,
          },
        });

        if (existingDocument) {
          this.logger.log(
            `Found duplicate document for authenticated user: ${userId}, document: ${documentName}`,
          );
          return existingDocument as IDocumentVault;
        }
      } else {
        // For guest users: retrieve email from payment table and check document_name + email combination
        const application = await this.prisma.application.findUnique({
          where: { id: applicationId },
          select: {
            payment_ids: true,
            guest_email: true,
          },
        });

        if (application?.payment_ids && application.payment_ids.length > 0) {
          // Use the first payment ID for guest email lookup
          const payment = await this.prisma.payment.findUnique({
            where: { id: application.payment_ids[0] },
            select: {
              guest_email: true,
              payment_type: true,
            },
          });

          if (payment?.guest_email && payment.payment_type === 'guest') {
            const existingDocument = await this.prisma.document_vault.findFirst(
              {
                where: {
                  document_name: documentName,
                  guest_email: payment.guest_email,
                },
              },
            );

            if (existingDocument) {
              this.logger.log(
                `Found duplicate document for guest user: ${payment.guest_email}, document: ${documentName}`,
              );
              return existingDocument as IDocumentVault;
            }
          }
        } else if (application?.guest_email) {
          // Fallback to application guest_email if no payment record
          const existingDocument = await this.prisma.document_vault.findFirst({
            where: {
              document_name: documentName,
              guest_email: application.guest_email,
            },
          });

          if (existingDocument) {
            this.logger.log(
              `Found duplicate document for guest user (fallback): ${application.guest_email}, document: ${documentName}`,
            );
            return existingDocument as IDocumentVault;
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Error checking for duplicate document: ${error.message}`,
        error.stack,
      );
      // Don't throw error - allow upload to proceed if duplicate check fails
      return null;
    }
  }

  /**
   * Upload document for specific application with enhanced folder structure
   * Uses format: documents/{applicationID}/{sanitized-filename}
   * Application context is stored in document_category and file_path structure
   *
   * @param file - The uploaded file
   * @param applicationId - Application ID for folder organization
   * @param metadata - Document metadata
   * @param userId - User ID (optional for guest uploads)
   * @returns Promise<IDocumentVault>
   */
  async uploadApplicationDocument(
    file: Express.Multer.File,
    applicationId: string,
    metadata: {
      document_name: string;
      document_type: DocumentType;
      document_category?: string;
      expiry_date?: Date;
      tags?: string[];
      guest_email?: string;
    },
    userId?: string,
  ): Promise<IDocumentVault> {
    try {
      this.logger.log(
        `Uploading application document: ${metadata.document_name} for application: ${applicationId}, user: ${userId || 'guest'}`,
      );

      // Check for duplicate documents before proceeding with upload
      const existingDocument = await this.checkForDuplicateDocument(
        metadata.document_name,
        applicationId,
        userId,
      );

      if (existingDocument) {
        this.logger.log(
          `Returning existing document instead of creating duplicate: ${existingDocument.id}`,
        );
        return existingDocument;
      }

      // Validate file
      this.validateFile(file);

      // Validate user exists if userId is provided
      let validatedUserId: string | null = null;
      if (userId) {
        const userExists = await this.validateUserExists(userId);
        if (userExists) {
          validatedUserId = userId;
          this.logger.debug(`User validation successful for user: ${userId}`);
        } else {
          this.logger.warn(
            `User ${userId} not found in database. Proceeding with guest upload.`,
          );
        }
      }

      // Upload file to storage using enhanced application-specific structure
      this.logger.debug(
        `Attempting to upload application document to storage: ${file.originalname}`,
      );
      const uploadResult = await this.mediaService.uploadApplicationDocument(
        file,
        applicationId,
        {
          preserveOriginalName: true,
          addTimestamp: true,
        },
      );

      // Check if upload was successful
      if (uploadResult.status !== 'OK') {
        this.logger.error(`Upload failed with status: ${uploadResult.status}`);
        throw new BadRequestException('Failed to upload file to storage');
      }

      this.logger.debug(
        `Application document uploaded successfully to: ${uploadResult.url}`,
      );

      // Create document record for application-specific upload
      // Note: metadata field was removed from schema in migration 20250616115742_simplify_document_vault_schema
      // Application context is maintained through the folder structure in file_path and document_category
      const documentData: any = {
        document_name: metadata.document_name,
        original_filename: file.originalname,
        document_type: metadata.document_type,
        document_category:
          metadata.document_category || `Application: ${applicationId}`,
        file_path: uploadResult.url,
        file_size: file.size,
        expiry_date: metadata.expiry_date,
        uploaded_by: validatedUserId || userId, // Keep original userId for audit trail
      };

      // Only set user_id if we have a validated user, otherwise leave it null for guest uploads
      if (validatedUserId) {
        documentData.user_id = validatedUserId;
      }

      // Set guest_email if provided or if we're falling back to guest upload
      if (metadata.guest_email || (userId && !validatedUserId)) {
        documentData.guest_email =
          metadata.guest_email || `fallback-${userId}@guest.local`;
      }

      const document = await this.prisma.document_vault.create({
        data: documentData,
      });

      this.logger.log(
        `Application document uploaded successfully: ${document.id}`,
      );
      return document as IDocumentVault;
    } catch (error) {
      this.logger.error(
        `Failed to upload application document: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get user's document vault with filtering options and pagination
   *
   * @param userId - User ID
   * @param filters - Filtering options
   * @returns Promise<{documents: IDocumentVault[], total: number, page: number, limit: number, totalPages: number}>
   */
  async getUserVault(
    userId: string,
    filters?: {
      document_type?: DocumentType;
      search?: string;
      page?: number;
      limit?: number;
    },
  ): Promise<{
    documents: IDocumentVault[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      this.logger.log(`Getting vault for user: ${userId}`);

      const page = filters?.page || 1;
      const limit = filters?.limit || 20;
      const skip = (page - 1) * limit;

      const whereClause: any = {
        user_id: userId,
      };

      // Apply filters
      if (filters?.document_type) {
        whereClause.document_type = filters.document_type;
      }

      if (filters?.search) {
        whereClause.OR = [
          { document_name: { contains: filters.search, mode: 'insensitive' } },
          {
            original_filename: {
              contains: filters.search,
              mode: 'insensitive',
            },
          },
          {
            document_category: {
              contains: filters.search,
              mode: 'insensitive',
            },
          },
        ];
      }

      const [documents, total] = await Promise.all([
        this.prisma.document_vault.findMany({
          where: whereClause,
          orderBy: { created_at: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.document_vault.count({ where: whereClause }),
      ]);

      return {
        documents: documents as IDocumentVault[],
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get user vault: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get user's document vault (legacy method for backward compatibility)
   *
   * @param userId - User ID
   * @param filters - Filtering options
   * @returns Promise<IDocumentVault[]>
   */
  async getUserVaultLegacy(
    userId: string,
    filters?: {
      document_type?: DocumentType;
      search?: string;
      page?: number;
      limit?: number;
    },
  ): Promise<IDocumentVault[]> {
    const result = await this.getUserVault(userId, filters);
    return result.documents;
  }

  /**
   * Check for expiring documents and return them
   *
   * @param daysAhead - Number of days to look ahead (default: 30)
   * @returns Promise<IDocumentVault[]>
   */
  async checkExpiringDocuments(
    daysAhead: number = 30,
  ): Promise<IDocumentVault[]> {
    try {
      this.logger.log(`Checking for documents expiring in ${daysAhead} days`);

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + daysAhead);

      const expiringDocuments = await this.prisma.document_vault.findMany({
        where: {
          expiry_date: {
            lte: futureDate,
            gte: new Date(),
          },
          expiry_reminder_sent: false,
        },
        orderBy: { expiry_date: 'asc' },
      });

      this.logger.log(`Found ${expiringDocuments.length} expiring documents`);
      return expiringDocuments as IDocumentVault[];
    } catch (error) {
      this.logger.error(
        `Failed to check expiring documents: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Link a document to an application
   *
   * @param documentId - Document ID
   * @param applicationId - Application ID
   * @returns Promise<void>
   */
  async linkDocumentToApplication(
    documentId: string,
    applicationId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Linking document ${documentId} to application ${applicationId}`,
      );

      // Verify document exists
      const document = await this.prisma.document_vault.findUnique({
        where: { id: documentId },
      });

      if (!document) {
        throw new NotFoundException('Document not found');
      }

      // Verify application exists
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
      });

      if (!application) {
        throw new NotFoundException('Application not found');
      }

      // Create the link
      await this.prisma.application_document.create({
        data: {
          application_id: applicationId,
          document_vault_id: documentId,
          request_reason: document.document_name, // Updated field name
          stage_order: 1, // Default stage order
          file_name: document.original_filename || document.document_name,
          file_url: document.file_path,
          required: true, // Default to required
          status: 'pending', // Default status
          upload_date: new Date(),
        },
      });

      this.logger.log(`Document linked successfully`);
    } catch (error) {
      this.logger.error(
        `Failed to link document to application: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate uploaded file
   *
   * @param file - Uploaded file
   * @throws BadRequestException if file is invalid
   */
  private validateFile(file: Express.Multer.File): void {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Check file size (25MB limit)
    const maxSize = 25 * 1024 * 1024; // 25MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size exceeds 25MB limit');
    }

    // Check allowed file types
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException('File type not supported');
    }
  }

  /**
   * Validate that a user exists in the database
   *
   * @param userId - User ID to validate
   * @returns Promise<boolean> - True if user exists, false otherwise
   */
  private async validateUserExists(userId: string): Promise<boolean> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true }, // Only select id for performance
      });
      return !!user;
    } catch (error) {
      this.logger.error(
        `Error validating user existence for userId ${userId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
