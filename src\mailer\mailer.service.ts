import { Injectable } from '@nestjs/common';

import { Resend } from 'resend';
import { ResendEmailDto } from './dto/mailer.dto';

@Injectable()
export class MailerService {
  async sendEmail(dto: ResendEmailDto) {
    const { from, to, subject, html } = dto;
    const resend = new Resend(process.env.EMAIL_API_KEY);
    const { data, error } = await resend.emails.send({
      from: from,
      to: [to],
      subject: subject,
      html: html,
    });

    if (error) {
      console.error(error);
    }
    return data;
  }
}
