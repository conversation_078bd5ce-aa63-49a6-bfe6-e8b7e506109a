import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

/**
 * JWT Token Types for different user roles
 */
export enum JwtTokenType {
  USER = 'user',
  ADMIN = 'admin',
  AGENT = 'agent',
  MENTOR = 'mentor',
  REFRESH = 'refresh',
  OTP = 'otp',
}

/**
 * JWT Secret Configuration mapping
 */
interface JwtSecretConfig {
  [key: string]: string;
}

@Injectable()
export class JwtGuard implements CanActivate {
  private readonly logger = new Logger(JwtGuard.name);
  private readonly jwtSecrets: JwtSecretConfig;

  constructor(private jwtService: JwtService) {
    // Initialize JWT secrets configuration with validation
    this.jwtSecrets = this.initializeJwtSecrets();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Bearer token is required');
    }

    try {
      // Attempt to verify token with multiple secrets
      const payload = await this.verifyTokenWithMultipleSecrets(token);
      request['user'] = payload;

      this.logger.debug(
        `Token verified successfully for user: ${payload.email}`,
      );
      return true;
    } catch (error) {
      this.logger.warn(`Token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Initialize JWT secrets from environment variables with validation
   */
  private initializeJwtSecrets(): JwtSecretConfig {
    const secrets: JwtSecretConfig = {
      [JwtTokenType.USER]: process.env.jwtSecretKey,
      [JwtTokenType.ADMIN]: process.env.jwtAdminSecretKey,
      [JwtTokenType.AGENT]: process.env.jwtAgentSecretKey,
      [JwtTokenType.MENTOR]: process.env.jwtMentorSecretKey,
    };

    // Validate that all required secrets are present
    const missingSecrets = Object.entries(secrets)
      .filter(([_, secret]) => !secret)
      .map(([type, _]) => type);

    if (missingSecrets.length > 0) {
      this.logger.error(
        `Missing JWT secrets for: ${missingSecrets.join(', ')}`,
      );
      throw new Error(
        `Missing JWT environment variables: ${missingSecrets.join(', ')}`,
      );
    }

    this.logger.log('JWT secrets initialized successfully');
    return secrets;
  }

  /**
   * Attempt to verify token with multiple secrets in priority order
   */
  private async verifyTokenWithMultipleSecrets(token: string): Promise<any> {
    // Define verification order - most commonly used first for performance
    const verificationOrder = [
      JwtTokenType.USER,
      JwtTokenType.ADMIN,
      JwtTokenType.AGENT,
      JwtTokenType.MENTOR,
    ];

    const errors: string[] = [];

    for (const tokenType of verificationOrder) {
      try {
        const secret = this.jwtSecrets[tokenType];
        const payload = await this.jwtService.verifyAsync(token, { secret });

        // Add token type to payload for downstream use
        payload.tokenType = tokenType;

        // this.logger.debug(`Token verified with ${tokenType} secret`);
        return payload;
      } catch (error) {
        errors.push(`${tokenType}: ${error.message}`);
        continue;
      }
    }

    // If all verification attempts failed, throw detailed error
    this.logger.warn(
      `Token verification failed with all secrets: ${errors.join('; ')}`,
    );
    throw new UnauthorizedException(
      'Token verification failed with all available secrets',
    );
  }

  /**
   * Extract Bearer token from Authorization header
   */
  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  /**
   * Verify token with specific secret (for specialized use cases)
   */
  async verifyTokenWithSecret(
    token: string,
    tokenType: JwtTokenType,
  ): Promise<any> {
    const secret = this.jwtSecrets[tokenType];
    if (!secret) {
      throw new UnauthorizedException(
        `JWT secret not configured for token type: ${tokenType}`,
      );
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, { secret });
      payload.tokenType = tokenType;
      return payload;
    } catch (error) {
      this.logger.warn(
        `Token verification failed for type ${tokenType}: ${error.message}`,
      );
      throw new UnauthorizedException(`Invalid ${tokenType} token`);
    }
  }

  /**
   * Get available JWT token types
   */
  getAvailableTokenTypes(): JwtTokenType[] {
    return Object.values(JwtTokenType);
  }
}
