#!/usr/bin/env ts-node

/**
 * Seed script for Dynamic Workflow System
 * Creates sample workflow templates, notification templates, and test data
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createWorkflowTemplates() {
  console.log('📋 Workflow templates functionality removed - skipping...');

  // REMOVED: workflow_template functionality
  // Following non-destructive patterns, commenting out instead of deleting
  /*
  // Service Workflow Template
  const serviceWorkflow = await prisma.workflow_template.upsert({
    where: { name: 'Standard Service Application Workflow' },
    update: {},
    create: {
      name: 'Standard Service Application Workflow',
      description: 'Complete workflow for service applications',
      application_type: 'service',
      service_type: 'service',
      estimated_duration: 30, // 30 days
      sla_threshold: 45, // 45 days
      steps_configuration: [
        {
          step_order: 1,
          step_name: 'Personal Details Collection',
          required_fields: [
            'name',
            'country',
            'dob',
            'contact_info',
            'emergency_contact',
          ],
          validation_rules: { required: true, min_age: 18 },
          completion_criteria: { all_fields_completed: true },
          estimated_duration: 2, // 2 hours
          assignee_role: 'applicant',
        },
        {
          step_order: 2,
          step_name: 'Document Upload Phase',
          required_fields: [
            'identification_document',
            'resume',
            'offer_letter',
            'educational_certificates',
          ],
          validation_rules: {
            file_types: ['pdf', 'jpg', 'png'],
            max_size: '10MB',
          },
          completion_criteria: { all_documents_uploaded: true },
          estimated_duration: 4, // 4 hours
          assignee_role: 'applicant',
        },
        {
          step_order: 3,
          step_name: 'Additional Information',
          required_fields: ['work_experience', 'references', 'medical_history'],
          validation_rules: { min_work_experience: 2 },
          completion_criteria: { all_sections_completed: true },
          estimated_duration: 3, // 3 hours
          assignee_role: 'applicant',
        },
        {
          step_order: 4,
          step_name: 'Review & Submission',
          required_fields: ['applicant_confirmation', 'terms_acceptance'],
          validation_rules: { terms_accepted: true },
          completion_criteria: { confirmed_and_submitted: true },
          estimated_duration: 1, // 1 hour
          assignee_role: 'applicant',
        },
        {
          step_order: 5,
          step_name: 'Initial Review',
          required_fields: ['completeness_check', 'document_verification'],
          validation_rules: { all_documents_present: true },
          completion_criteria: { initial_review_completed: true },
          estimated_duration: 24, // 24 hours
          assignee_role: 'admin',
        },
        {
          step_order: 6,
          step_name: 'Document Verification',
          required_fields: ['document_authenticity', 'eligibility_check'],
          validation_rules: { documents_verified: true },
          completion_criteria: { verification_completed: true },
          estimated_duration: 72, // 72 hours
          assignee_role: 'admin',
        },
        {
          step_order: 7,
          step_name: 'Final Approval',
          required_fields: ['final_decision', 'approval_notes'],
          validation_rules: { decision_made: true },
          completion_criteria: { final_decision_recorded: true },
          estimated_duration: 48, // 48 hours
          assignee_role: 'admin',
        },
      ],
    },
  });

  // Package Workflow Template
  const packageWorkflow = await prisma.workflow_template.upsert({
    where: { name: 'Package Service Workflow' },
    update: {},
    create: {
      name: 'Package Service Workflow',
      description: 'Workflow for package-based services',
      application_type: 'package',
      estimated_duration: 14, // 14 days
      sla_threshold: 21, // 21 days
      steps_configuration: [
        {
          step_order: 1,
          step_name: 'Package Selection Confirmation',
          required_fields: ['package_details', 'service_requirements'],
          completion_criteria: { package_confirmed: true },
          estimated_duration: 1,
          assignee_role: 'applicant',
        },
        {
          step_order: 2,
          step_name: 'Initial Consultation',
          required_fields: ['consultation_scheduled', 'requirements_gathered'],
          completion_criteria: { consultation_completed: true },
          estimated_duration: 24,
          assignee_role: 'admin',
        },
        {
          step_order: 3,
          step_name: 'Service Delivery',
          required_fields: ['service_execution', 'progress_updates'],
          completion_criteria: { service_delivered: true },
          estimated_duration: 240, // 10 days
          assignee_role: 'admin',
        },
        {
          step_order: 4,
          step_name: 'Quality Review',
          required_fields: ['quality_check', 'client_feedback'],
          completion_criteria: { quality_approved: true },
          estimated_duration: 24,
          assignee_role: 'admin',
        },
      ],
    },
  });
  */

  console.log(`✅ Workflow templates functionality removed - skipped`);
  return { serviceWorkflow: null, packageWorkflow: null };
}

async function createNotificationTemplates() {
  console.log('📧 Creating notification templates...');

  const templates = [
    {
      template_name: 'application_created',
      template_type: 'email',
      category: 'workflow',
      subject: 'Application Created - {{application_number}}',
      body_template: `Dear {{user_name}},

Your application {{application_number}} has been successfully created.

Application Type: {{application_type}}
Status: {{status}}
Created: {{created_at}}

Next Steps:
{{next_steps}}

Best regards,
Career Ireland Team`,
      trigger_event: 'application_created',
      trigger_conditions: { status: 'Draft' },
    },
    {
      template_name: 'document_upload_reminder',
      template_type: 'email',
      category: 'reminder',
      subject: 'Document Upload Required - {{application_number}}',
      body_template: `Dear {{user_name}},

We need you to upload the following documents for your application {{application_number}}:

{{required_documents}}

Please upload these documents at your earliest convenience to avoid delays.

Upload Link: {{upload_link}}

Best regards,
Career Ireland Team`,
      trigger_event: 'document_upload_required',
      delay_minutes: 1440, // 24 hours
      trigger_conditions: { step_name: 'Document Upload Phase' },
    },
    {
      template_name: 'application_approved',
      template_type: 'email',
      category: 'workflow',
      subject: 'Application Approved - {{application_number}}',
      body_template: `Dear {{user_name}},

Congratulations! Your application {{application_number}} has been approved.

Application Type: {{application_type}}
Approval Date: {{approved_at}}
Next Steps: {{next_steps}}

You will receive further instructions shortly.

Best regards,
Career Ireland Team`,
      trigger_event: 'application_approved',
      trigger_conditions: { status: 'Approved' },
    },
    {
      template_name: 'step_completed',
      template_type: 'email',
      category: 'workflow',
      subject: 'Step Completed - {{step_name}}',
      body_template: `Dear {{user_name}},

The step "{{step_name}}" for your application {{application_number}} has been completed.

Current Status: {{application_status}}
Next Step: {{next_step_name}}

{{step_completion_notes}}

Best regards,
Career Ireland Team`,
      trigger_event: 'step_completed',
      trigger_conditions: { step_status: 'Completed' },
    },
    {
      template_name: 'document_expiry_warning',
      template_type: 'email',
      category: 'reminder',
      subject: 'Document Expiry Warning - {{document_name}}',
      body_template: `Dear {{user_name}},

Your document "{{document_name}}" will expire on {{expiry_date}}.

Please upload a renewed version to avoid any delays in processing your applications.

Upload Link: {{upload_link}}

Best regards,
Career Ireland Team`,
      trigger_event: 'document_expiry_warning',
      delay_minutes: 0,
      trigger_conditions: { days_until_expiry: 30 },
    },
  ];

  const createdTemplates = [];
  for (const template of templates) {
    const created = await prisma.notification_template.upsert({
      where: { template_name: template.template_name },
      update: {},
      create: template,
    });
    createdTemplates.push(created);
  }

  console.log(`✅ Created ${createdTemplates.length} notification templates`);
  return createdTemplates;
}

async function createSampleData() {
  console.log('🎯 Creating sample application data...');

  // Find existing user or create one
  let testUser = await prisma.user.findFirst({
    where: { email: '<EMAIL>' },
  });

  if (!testUser) {
    testUser = await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        provider: 'credentials',
      },
    });
  }

  // Create sample payment
  const testPayment = await prisma.payment.create({
    data: {
      amount: 2500,
      status: 'completed',
      payment_type: 'user',
      service_type: 'service',
      userId: testUser.id,
    },
  });

  // REMOVED: workflow_template functionality
  // Following non-destructive patterns, commenting out template-based application creation
  /*
  // Get workflow template
  const workflowTemplate = await prisma.workflow_template.findFirst({
    where: { name: 'Standard Service Application Workflow' },
  });

  if (!workflowTemplate) {
    throw new Error('Workflow template not found');
  }
  */

  // Create sample application (without workflow template)
  const application = await prisma.application.create({
    data: {
      application_number: `APP-${Date.now()}`,
      // REMOVED: application_type field - field removed from schema
      // application_type: 'service',
      service_type: 'service',
      service_id: testPayment.id,
      current_step: '1', // Start at step 1
      // workflow_template_id: workflowTemplate.id, // REMOVED
      status: 'Draft',
      priority_level: 'Medium',
      steps: {}, // Add empty steps JSON
      // REMOVED: metadata field - field removed from schema
      // metadata: {
      //   service_category: 'Professional Services',
      //   service_location: 'Ireland',
      //   service_type_detail: 'Consultation',
      //   urgency: 'Standard',
      // },
      created_by: testUser.id,
      // Connect user relationship properly
      user: {
        connect: { id: testUser.id }
      },
      // REMOVED: updated_by field - field not in schema
    },
  });

  // REMOVED: workflow template-based step creation
  // Following non-destructive patterns, commenting out
  /*
  // Create workflow steps
  const stepsConfig = workflowTemplate.steps_configuration as any[];
  for (const stepConfig of stepsConfig.slice(0, 3)) {
    // Create first 3 steps
    await prisma.application_step.create({
      data: {
        application_id: application.id,
        step_name: stepConfig.step_name,
        step_order: stepConfig.step_order,
        status: stepConfig.step_order === 1 ? 'In_Progress' : 'Not_Started',
        required_fields: stepConfig.required_fields,
        validation_rules: stepConfig.validation_rules,
        completion_criteria: stepConfig.completion_criteria,
        estimated_duration: stepConfig.estimated_duration,
        assignee_role: stepConfig.assignee_role,
      },
    });
  }
  */

  console.log(
    `✅ Created sample application: ${application.application_number}`,
  );
  return { testUser, application };
}

async function main() {
  console.log('🌱 Seeding Dynamic Workflow System data...\n');

  try {
    const { serviceWorkflow, packageWorkflow } =
      await createWorkflowTemplates();
    const notificationTemplates = await createNotificationTemplates();
    const { testUser, application } = await createSampleData();

    console.log('\n🎉 Seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   Workflow Templates: 2`);
    console.log(`   Notification Templates: ${notificationTemplates.length}`);
    console.log(`   Sample Applications: 1`);
    console.log(`   Test User: ${testUser.email}`);
    console.log(`   Sample Application: ${application.application_number}`);
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
