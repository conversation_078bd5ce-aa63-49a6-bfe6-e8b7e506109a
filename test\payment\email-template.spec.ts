/**
 * Email Template Tests
 *
 * Comprehensive test suite for email template rendering and sending functionality
 * in the unified payment system. Tests both successful template rendering and
 * fallback scenarios when template rendering fails.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import { Test, TestingModule } from '@nestjs/testing';
import { render } from '@react-email/components';
import { UnifiedPaymentService } from '../../src/payment/unified-payment.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { STRIPE_CLIENT } from '../../src/config/stripe.config';
import ServicePaymentSuccessEmail from '../../src/template/service';
import PurchaseNotificationEmail from '../../src/template/purchase-notification';
import {
  getFallbackCustomerTemplate,
  getFallbackAdminTemplate,
} from '../../src/template/fallback-email-template';
import { ServiceType, PaymentType } from '../../src/payment/dto/payment.dto';
import { ApplicationIntegrationService } from '../../src/application/services/application-integration.service';

// Mock the render function
jest.mock('@react-email/components', () => ({
  render: jest.fn(),
}));

// Mock the email templates
jest.mock('../../src/template/service', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('../../src/template/purchase-notification', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('Email Template Rendering', () => {
  let service: UnifiedPaymentService;
  let mailerService: MailerService;
  let loggerService: LoggerService;
  let prismaService: PrismaService;

  const mockStripeClient = {
    webhooks: {
      constructEvent: jest.fn(),
    },
    checkout: {
      sessions: {
        create: jest.fn(),
      },
    },
  };

  const mockMailerService = {
    sendEmail: jest.fn(),
  };

  const mockLoggerService = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    logPayment: jest.fn(),
  };

  const mockPrismaService = {
    payment: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      aggregate: jest.fn(),
      groupBy: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    service: {
      findUnique: jest.fn(),
    },
    packages: {
      findUnique: jest.fn(),
    },
    immigration_service: {
      findUnique: jest.fn(),
    },
    training: {
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UnifiedPaymentService,
        {
          provide: STRIPE_CLIENT,
          useValue: mockStripeClient,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ApplicationIntegrationService,
          useValue: {
            createApplicationFromPayment: jest.fn(),
            validateWorkflowTemplate: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UnifiedPaymentService>(UnifiedPaymentService);
    mailerService = module.get<MailerService>(MailerService);
    loggerService = module.get<LoggerService>(LoggerService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('Customer Email Template Rendering', () => {
    const mockPayment = {
      id: 'payment-123',
      status: 'paid',
      service_type: ServiceType.SERVICE,
      payment_type: PaymentType.USER,
      userId: 'user-123',
      createdAt: new Date(),
    };

    const mockServiceData = {
      service: {
        id: 'service-123',
        name: 'Career Consultation',
        amount: 100,
      },
      user: {
        email: '<EMAIL>',
        name: 'John Doe',
      },
    };

    const mockEmailConfig = {
      customerSubject: 'Payment Confirmation',
      serviceName: 'Professional Service',
    };

    it('should successfully render and send customer email template', async () => {
      const mockRenderedHtml =
        '<html><body>Payment Success Email</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      // Access private method using bracket notation for testing
      await (service as any).sendCustomerNotification(
        mockPayment,
        mockServiceData,
        mockEmailConfig,
      );

      expect(render).toHaveBeenCalledWith({});
      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: {
          id: mockServiceData.service.id,
          createdAt: mockPayment.createdAt,
          status: mockPayment.status,
          name: mockServiceData.service.name,
          amount: mockServiceData.service.amount,
          // Mentor should be undefined for service type since mockServiceData.service.mentor is undefined
          mentor: undefined,
        },
        user: {
          email: mockServiceData.user.email,
          name: mockServiceData.user.name,
        },
      });

      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL,
        to: mockServiceData.user.email,
        subject: mockEmailConfig.customerSubject,
        cc: [],
        html: mockRenderedHtml,
      });

      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Customer payment notification sent successfully',
        {
          paymentId: mockPayment.id,
          customerEmail: mockServiceData.user.email,
          serviceType: mockPayment.service_type,
        },
      );
    });

    it('should use fallback template when rendering fails', async () => {
      const renderError = new Error('Template rendering failed');
      (render as jest.Mock).mockRejectedValue(renderError);

      await (service as any).sendCustomerNotification(
        mockPayment,
        mockServiceData,
        mockEmailConfig,
      );

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Failed to send customer payment notification',
        renderError,
        {
          paymentId: mockPayment.id,
          customerEmail: mockServiceData.user.email,
          serviceType: mockPayment.service_type,
        },
      );

      // Should send fallback email
      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL,
        to: mockServiceData.user.email,
        subject: mockEmailConfig.customerSubject,
        cc: [],
        html: expect.stringContaining('Payment Successful'),
      });
    });

    it('should handle guest customer email', async () => {
      const guestPayment = {
        ...mockPayment,
        payment_type: PaymentType.GUEST,
        userId: null,
        guest_email: '<EMAIL>',
        guest_name: 'Jane Guest',
      };

      const guestServiceData = {
        ...mockServiceData,
        user: null,
      };

      const mockRenderedHtml =
        '<html><body>Guest Payment Success</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);

      await (service as any).sendCustomerNotification(
        guestPayment,
        guestServiceData,
        mockEmailConfig,
      );

      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL,
        to: guestPayment.guest_email,
        subject: mockEmailConfig.customerSubject,
        cc: [],
        html: mockRenderedHtml,
      });
    });

    it('should skip email when no customer email available', async () => {
      const paymentWithoutEmail = {
        ...mockPayment,
        userId: null,
        guest_email: null,
      };

      const serviceDataWithoutUser = {
        ...mockServiceData,
        user: null,
      };

      await (service as any).sendCustomerNotification(
        paymentWithoutEmail,
        serviceDataWithoutUser,
        mockEmailConfig,
      );

      expect(mockLoggerService.warn).toHaveBeenCalledWith(
        'No customer email available for notification',
        {
          paymentId: paymentWithoutEmail.id,
          paymentType: paymentWithoutEmail.payment_type,
        },
      );

      expect(mockMailerService.sendEmail).not.toHaveBeenCalled();
    });

    it('should include mentor name only for service type payments', async () => {
      // Test with service type - should include mentor
      const servicePayment = {
        ...mockPayment,
        service_type: ServiceType.SERVICE,
      };

      const serviceDataWithMentor = {
        service: {
          id: 'service-123',
          name: 'Career Consultation',
          amount: 100,
          mentor: 'John Mentor',
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      const mockRenderedHtml =
        '<html><body>Service Payment Success</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendCustomerNotification(
        servicePayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: 'John Mentor', // Should include mentor for service type
        }),
        user: expect.any(Object),
      });

      // Test with package type - should not include mentor
      const packagePayment = {
        ...mockPayment,
        service_type: ServiceType.PACKAGE,
      };

      jest.clearAllMocks();
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendCustomerNotification(
        packagePayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: undefined, // Should not include mentor for package type
        }),
        user: expect.any(Object),
      });
    });

    it('should not include mentor name for immigration service type', async () => {
      const immigrationPayment = {
        ...mockPayment,
        service_type: ServiceType.IMMIGRATION,
      };

      const serviceDataWithMentor = {
        service: {
          id: 'immigration-123',
          name: 'Visa Consultation',
          amount: 200,
          mentor: 'Immigration Expert',
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      const mockRenderedHtml =
        '<html><body>Immigration Payment Success</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendCustomerNotification(
        immigrationPayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: undefined, // Should NOT include mentor for immigration type
        }),
        user: expect.any(Object),
      });
    });

    it('should not include mentor name for training service type', async () => {
      const trainingPayment = {
        ...mockPayment,
        service_type: ServiceType.TRAINING,
      };

      const serviceDataWithMentor = {
        service: {
          id: 'training-123',
          name: 'Professional Training',
          amount: 150,
          mentor: 'Training Instructor',
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      const mockRenderedHtml =
        '<html><body>Training Payment Success</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendCustomerNotification(
        trainingPayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: undefined, // Should NOT include mentor for training type
        }),
        user: expect.any(Object),
      });
    });

    it('should handle mentor logic correctly for all service types', async () => {
      const serviceDataWithMentor = {
        service: {
          id: 'test-service-123',
          name: 'Test Service',
          amount: 100,
          mentor: 'Test Mentor',
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      const mockRenderedHtml = '<html><body>Test Email</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      // Test all service types
      const testCases = [
        {
          serviceType: ServiceType.SERVICE,
          shouldIncludeMentor: true,
          description: 'SERVICE type should include mentor',
        },
        {
          serviceType: ServiceType.PACKAGE,
          shouldIncludeMentor: false,
          description: 'PACKAGE type should NOT include mentor',
        },
        {
          serviceType: ServiceType.IMMIGRATION,
          shouldIncludeMentor: false,
          description: 'IMMIGRATION type should NOT include mentor',
        },
        {
          serviceType: ServiceType.TRAINING,
          shouldIncludeMentor: false,
          description: 'TRAINING type should NOT include mentor',
        },
      ];

      for (const testCase of testCases) {
        jest.clearAllMocks();
        (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
        (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

        const payment = {
          ...mockPayment,
          service_type: testCase.serviceType,
        };

        await (service as any).sendCustomerNotification(
          payment,
          serviceDataWithMentor,
          mockEmailConfig,
        );

        const expectedMentor = testCase.shouldIncludeMentor
          ? 'Test Mentor'
          : undefined;

        expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
          service: expect.objectContaining({
            mentor: expectedMentor,
          }),
          user: expect.any(Object),
        });

        // Log for clarity
        console.log(`✓ ${testCase.description}: mentor = ${expectedMentor}`);
      }
    });

    it('should handle edge cases for mentor logic', async () => {
      const mockRenderedHtml = '<html><body>Test Email</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      // Test case 1: Service type with null mentor
      const serviceDataWithNullMentor = {
        service: {
          id: 'service-123',
          name: 'Career Consultation',
          amount: 100,
          mentor: null,
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      const servicePayment = {
        ...mockPayment,
        service_type: ServiceType.SERVICE,
      };

      await (service as any).sendCustomerNotification(
        servicePayment,
        serviceDataWithNullMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: null, // Should pass through null for service type
        }),
        user: expect.any(Object),
      });

      // Test case 2: Service type with undefined mentor
      jest.clearAllMocks();
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      const serviceDataWithUndefinedMentor = {
        service: {
          id: 'service-123',
          name: 'Career Consultation',
          amount: 100,
          mentor: undefined,
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      await (service as any).sendCustomerNotification(
        servicePayment,
        serviceDataWithUndefinedMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: undefined, // Should pass through undefined for service type
        }),
        user: expect.any(Object),
      });

      // Test case 3: Non-service type with mentor present (should ignore mentor)
      jest.clearAllMocks();
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      const packagePayment = {
        ...mockPayment,
        service_type: ServiceType.PACKAGE,
      };

      const serviceDataWithMentor = {
        service: {
          id: 'package-123',
          name: 'Career Package',
          amount: 200,
          mentor: 'Should Be Ignored',
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      await (service as any).sendCustomerNotification(
        packagePayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: undefined, // Should be undefined regardless of mentor value for non-service types
        }),
        user: expect.any(Object),
      });
    });

    it('should verify exact requirement: service_type === "Service" includes mentor, others do not', async () => {
      const mockRenderedHtml = '<html><body>Test Email</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      // Test data with mentor present
      const serviceDataWithMentor = {
        service: {
          id: 'test-123',
          name: 'Test Service',
          amount: 100,
          mentor: 'John Mentor',
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      // Test 1: service_type === "Service" should include mentor
      const servicePayment = {
        ...mockPayment,
        service_type: 'service', // Exact string as mentioned in requirements
      };

      await (service as any).sendCustomerNotification(
        servicePayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: 'John Mentor', // Should include mentor for service type
        }),
        user: expect.any(Object),
      });

      // Test 2: service_type !== "Service" should NOT include mentor
      const nonServicePayment = {
        ...mockPayment,
        service_type: 'package', // Not "service"
      };

      jest.clearAllMocks();
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendCustomerNotification(
        nonServicePayment,
        serviceDataWithMentor,
        mockEmailConfig,
      );

      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: undefined, // Should NOT include mentor for non-service types
        }),
        user: expect.any(Object),
      });
    });
  });

  describe('Admin Email Template Rendering', () => {
    const mockPayment = {
      id: 'payment-456',
      status: 'paid',
      service_type: ServiceType.IMMIGRATION,
      payment_type: PaymentType.USER,
      userId: 'user-456',
      createdAt: new Date(),
    };

    const mockServiceData = {
      service: {
        id: 'immigration-123',
        name: 'Visa Consultation',
        amount: 200,
      },
      user: {
        email: '<EMAIL>',
        name: 'John Doe',
      },
    };

    const mockEmailConfig = {
      adminSubject: 'New Immigration Service Purchase',
      serviceName: 'Immigration Service',
    };

    it('should successfully render and send admin email template', async () => {
      const mockRenderedHtml = '<html><body>Admin Notification</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (PurchaseNotificationEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendAdminNotification(
        mockPayment,
        mockServiceData,
        mockEmailConfig,
      );

      expect(render).toHaveBeenCalledWith({});
      expect(PurchaseNotificationEmail).toHaveBeenCalledWith({
        name: mockEmailConfig.serviceName,
        service: {
          id: mockServiceData.service.id,
          name: mockServiceData.service.name,
          amount: mockServiceData.service.amount,
          createdAt: mockPayment.createdAt,
          status: mockPayment.status,
        },
        user: {
          email: mockServiceData.user.email,
          name: mockServiceData.user.name,
        },
      });

      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: mockEmailConfig.adminSubject,
        cc: [],
        html: mockRenderedHtml,
      });

      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Admin payment notification sent successfully',
        {
          paymentId: mockPayment.id,
          customerEmail: mockServiceData.user.email,
          serviceType: mockPayment.service_type,
        },
      );
    });

    it('should use fallback template when admin template rendering fails', async () => {
      const renderError = new Error('Admin template rendering failed');
      (render as jest.Mock).mockRejectedValue(renderError);

      await (service as any).sendAdminNotification(
        mockPayment,
        mockServiceData,
        mockEmailConfig,
      );

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Failed to send admin payment notification',
        renderError,
        {
          paymentId: mockPayment.id,
          customerEmail: mockServiceData.user.email,
          serviceType: mockPayment.service_type,
        },
      );

      // Should send fallback email
      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: mockEmailConfig.adminSubject,
        cc: [],
        html: expect.stringContaining('New Payment Received'),
      });
    });

    it('should fetch mentor name from database for SERVICE type and include in email', async () => {
      // This test verifies the complete flow: database fetch -> mentor extraction -> email inclusion
      const servicePayment = {
        ...mockPayment,
        service_type: ServiceType.SERVICE,
      };

      // Mock the getServiceDataForNotification to return service data with mentor
      const serviceDataWithMentorFromDB = {
        service: {
          id: 'service-123',
          name: 'Career Consultation',
          amount: 100,
          mentor: 'Database Mentor Name', // This should come from the database
        },
        user: {
          email: '<EMAIL>',
          name: 'John Doe',
        },
      };

      // Mock the service's getServiceDataForNotification method
      jest
        .spyOn(service as any, 'getServiceDataForNotification')
        .mockResolvedValue(serviceDataWithMentorFromDB);

      // Mock the getEmailConfig method to return proper config
      const serviceEmailConfig = {
        adminSubject: 'New Service Payment - Career Ireland',
        customerSubject: 'Payment Confirmation - Career Ireland',
        serviceName: 'Career Consultation',
      };
      jest
        .spyOn(service as any, 'getEmailConfig')
        .mockReturnValue(serviceEmailConfig);

      const mockRenderedHtml =
        '<html><body>Service Payment Success</body></html>';
      (render as jest.Mock).mockResolvedValue(mockRenderedHtml);
      (ServicePaymentSuccessEmail as jest.Mock).mockReturnValue({});

      await (service as any).sendCustomerNotification(
        servicePayment,
        serviceDataWithMentorFromDB,
        serviceEmailConfig,
      );

      // Verify that the mentor name from database is included in the email
      expect(ServicePaymentSuccessEmail).toHaveBeenCalledWith({
        service: expect.objectContaining({
          mentor: 'Database Mentor Name', // Should include the mentor name from database
        }),
        user: expect.any(Object),
      });

      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: process.env.EMAIL,
        to: '<EMAIL>',
        subject: 'Payment Confirmation - Career Ireland',
        cc: [],
        html: mockRenderedHtml,
      });
    });
  });

  describe('Fallback Templates', () => {
    it('should generate proper fallback customer template', () => {
      const serviceName = 'Test Service';
      const customerName = 'Test Customer';

      const fallbackHtml = getFallbackCustomerTemplate(
        serviceName,
        customerName,
      );

      expect(fallbackHtml).toContain('Payment Successful');
      expect(fallbackHtml).toContain(serviceName);
      expect(fallbackHtml).toContain(customerName);
      expect(fallbackHtml).toContain('Career Ireland');
    });

    it('should generate proper fallback admin template', () => {
      const serviceName = 'Test Service';
      const customerName = 'Test Customer';
      const customerEmail = '<EMAIL>';

      const fallbackHtml = getFallbackAdminTemplate(
        serviceName,
        customerName,
        customerEmail,
      );

      expect(fallbackHtml).toContain('New Payment Received');
      expect(fallbackHtml).toContain(serviceName);
      expect(fallbackHtml).toContain(customerName);
      expect(fallbackHtml).toContain(customerEmail);
    });
  });
});
