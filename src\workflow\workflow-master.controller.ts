/**
 * Workflow Master Controller
 *
 * This controller provides REST API endpoints for workflow master management.
 * All endpoints require admin authentication and include comprehensive
 * validation, error handling, and OpenAPI documentation.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { WorkflowMasterService } from './workflow-master.service';
import {
  CreateWorkflowMasterDto,
  UpdateWorkflowMasterDto,
  WorkflowMasterFiltersDto,
  WorkflowMasterResponseDto,
  PaginatedWorkflowMasterResponseDto,
} from './dto/workflow-master.dto';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { GetUser } from '../decorator/user.decorator';
import { IJWTPayload } from '../types/auth';

@ApiTags('Workflow Master')
@Controller('workflow-master')
@UseGuards(JwtAdmin)
@ApiBearerAuth()
export class WorkflowMasterController {
  private readonly logger = new Logger(WorkflowMasterController.name);

  constructor(private readonly workflowMasterService: WorkflowMasterService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new workflow master',
    description:
      'Creates a new workflow master template. Admin access required.',
  })
  @ApiResponse({
    status: 201,
    description: 'Workflow master created successfully',
    type: WorkflowMasterResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 409,
    description: 'Workflow master with this name already exists',
  })
  async create(
    @Body() createWorkflowMasterDto: CreateWorkflowMasterDto,
    @GetUser() user: IJWTPayload,
  ): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(
        `Admin ${user.email} creating workflow master: ${createWorkflowMasterDto.name}`,
      );

      const result = await this.workflowMasterService.create(
        createWorkflowMasterDto,
        user,
      );

      this.logger.log(`Workflow master created successfully: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all workflow masters',
    description:
      'Retrieves all workflow masters with pagination and filtering. Admin access required.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term for workflow name',
    example: 'immigration',
  })
  @ApiQuery({
    name: 'is_active',
    required: false,
    description: 'Filter by active status',
    example: true,
  })
  @ApiQuery({
    name: 'sort_by',
    required: false,
    description: 'Sort field',
    example: 'created_at',
  })
  @ApiQuery({
    name: 'sort_order',
    required: false,
    description: 'Sort order',
    example: 'desc',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow masters retrieved successfully',
    type: PaginatedWorkflowMasterResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async findAll(
    @Query() filters: WorkflowMasterFiltersDto,
  ): Promise<PaginatedWorkflowMasterResponseDto> {
    try {
      this.logger.log('Retrieving workflow masters with filters', filters);

      const result = await this.workflowMasterService.findAll(filters);

      this.logger.log(
        `Retrieved ${result.data.length} workflow masters (page ${result.page}/${result.totalPages})`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve workflow masters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('active')
  @ApiOperation({
    summary: 'Get active workflow masters',
    description:
      'Retrieves all active workflow masters. Admin access required.',
  })
  @ApiResponse({
    status: 200,
    description: 'Active workflow masters retrieved successfully',
    type: [WorkflowMasterResponseDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async findActive(): Promise<WorkflowMasterResponseDto[]> {
    try {
      this.logger.log('Retrieving active workflow masters');

      const result = await this.workflowMasterService.findActive();

      this.logger.log(`Retrieved ${result.length} active workflow masters`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve active workflow masters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a workflow master by ID',
    description:
      'Retrieves a specific workflow master by its ID. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow master retrieved successfully',
    type: WorkflowMasterResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow master not found',
  })
  async findOne(@Param('id') id: string): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(`Retrieving workflow master: ${id}`);

      const result = await this.workflowMasterService.findOne(id);

      this.logger.log(`Workflow master retrieved successfully: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update a workflow master',
    description: 'Updates an existing workflow master. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow master updated successfully',
    type: WorkflowMasterResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow master not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Workflow master with this name already exists',
  })
  async update(
    @Param('id') id: string,
    @Body() updateWorkflowMasterDto: UpdateWorkflowMasterDto,
    @GetUser() user: IJWTPayload,
  ): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(`Admin ${user.email} updating workflow master: ${id}`);

      const result = await this.workflowMasterService.update(
        id,
        updateWorkflowMasterDto,
        user,
      );

      this.logger.log(`Workflow master updated successfully: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to update workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Patch(':id/toggle-active')
  @ApiOperation({
    summary: 'Toggle workflow master active status',
    description:
      'Toggles the active status of a workflow master. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow master active status toggled successfully',
    type: WorkflowMasterResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow master not found',
  })
  async toggleActive(
    @Param('id') id: string,
    @GetUser() user: IJWTPayload,
  ): Promise<WorkflowMasterResponseDto> {
    try {
      this.logger.log(
        `Admin ${user.email} toggling active status for workflow master: ${id}`,
      );

      const result = await this.workflowMasterService.toggleActive(id, user);

      this.logger.log(
        `Workflow master active status toggled: ${result.id} (${result.is_active})`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to toggle workflow master active status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete a workflow master',
    description:
      'Deletes a workflow master if it is not in use. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow master ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow master deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow master not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Cannot delete workflow master as it is currently in use',
  })
  async remove(@Param('id') id: string): Promise<{ message: string }> {
    try {
      this.logger.log(`Deleting workflow master: ${id}`);

      await this.workflowMasterService.remove(id);

      this.logger.log(`Workflow master deleted successfully: ${id}`);
      return { message: 'Workflow master deleted successfully' };
    } catch (error) {
      this.logger.error(
        `Failed to delete workflow master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
