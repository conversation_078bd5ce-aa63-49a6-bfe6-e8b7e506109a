/**
 * Enhanced Workflow Engine Service Tests
 * Task 3: Comprehensive tests for enhanced workflow features
 */

import { Test, TestingModule } from '@nestjs/testing';
import { EnhancedWorkflowEngineService } from '../../src/application/services/enhanced-workflow-engine.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { WorkflowStepStatus, PriorityLevel } from '@prisma/client';

describe('EnhancedWorkflowEngineService', () => {
  let service: EnhancedWorkflowEngineService;
  let prismaService: PrismaService;
  let notificationService: NotificationService;

  const mockApplication = {
    id: 'app-123',
    application_number: 'IMM-2024-000001',
    application_type: 'immigration',
    service_type: 'immigration',
    status: 'Draft',
    priority_level: PriorityLevel.Medium,
    sla_deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    estimated_duration: 168, // 1 week
    created_at: new Date(),
    updated_at: new Date(),
    completed_at: null,
  };

  const mockEnhancedTemplate = {
    id: 'template-123',
    name: 'Enhanced Immigration Workflow',
    application_type: 'immigration',
    service_type: 'immigration',
    is_active: true,
    version: '2.0',
    steps_configuration: [
      {
        step_name: 'Document Collection',
        step_order: 1,
        estimated_duration: 24,
        assignee_role: 'applicant',
        branching_rules: [
          {
            condition: 'document_type === "urgent"',
            target_step: 3,
          },
        ],
        parallel_group: null,
        escalation_rules: [
          {
            trigger: 'overdue',
            threshold_hours: 48,
            action: 'notify_manager',
          },
        ],
      },
      {
        step_name: 'Initial Review',
        step_order: 2,
        estimated_duration: 48,
        assignee_role: 'admin',
        parallel_group: 'review_group',
      },
      {
        step_name: 'Background Check',
        step_order: 3,
        estimated_duration: 72,
        assignee_role: 'admin',
        parallel_group: 'review_group',
      },
    ],
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnhancedWorkflowEngineService,
        {
          provide: PrismaService,
          useValue: {
            application: {
              findUnique: jest.fn(),
              findMany: jest.fn(),
              update: jest.fn(),
              count: jest.fn(),
              fields: {
                sla_deadline: 'sla_deadline',
              },
            },
            application_step: {
              createMany: jest.fn(),
              findMany: jest.fn(),
              findFirst: jest.fn(),
              update: jest.fn(),
            },
            // REMOVED: workflow_template - functionality removed
            notification_queue: {
              create: jest.fn(),
            },
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
        {
          provide: NotificationService,
          useValue: {
            sendNotification: jest.fn(),
            scheduleNotification: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EnhancedWorkflowEngineService>(
      EnhancedWorkflowEngineService,
    );
    prismaService = module.get<PrismaService>(PrismaService);
    notificationService = module.get<NotificationService>(NotificationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initializeEnhancedWorkflow', () => {
    it('should initialize workflow with enhanced features', async () => {
      const mockSteps = [
        {
          id: 'step-1',
          application_id: 'app-123',
          step_name: 'Document Collection',
          step_order: 1,
          status: WorkflowStepStatus.Not_Started,
        },
        {
          id: 'step-2',
          application_id: 'app-123',
          step_name: 'Initial Review',
          step_order: 2,
          status: WorkflowStepStatus.Not_Started,
        },
      ];

      jest
        .spyOn(prismaService.application_step, 'createMany')
        .mockResolvedValue({ count: 2 });
      jest
        .spyOn(prismaService.application_step, 'findMany')
        .mockResolvedValue(mockSteps as any);

      const options = {
        enableBranching: true,
        enableParallelExecution: true,
        customStepConfiguration: {},
      };

      const result = await service.initializeEnhancedWorkflow(
        mockApplication as any,
        mockEnhancedTemplate as any,
        options,
      );

      expect(prismaService.application_step.createMany).toHaveBeenCalled();
      expect(result).toEqual(mockSteps);
    });

    it('should handle invalid steps configuration', async () => {
      const invalidTemplate = {
        ...mockEnhancedTemplate,
        steps_configuration: 'invalid',
      };

      await expect(
        service.initializeEnhancedWorkflow(
          mockApplication as any,
          invalidTemplate as any,
        ),
      ).rejects.toThrow('Invalid steps configuration in workflow template');
    });
  });

  describe('advanceEnhancedWorkflow', () => {
    it('should advance workflow with enhanced features', async () => {
      const currentStep = {
        id: 'step-1',
        application_id: 'app-123',
        step_name: 'Document Collection',
        step_order: 1,
        status: WorkflowStepStatus.In_Progress,
        application: mockApplication,
      };

      const nextStep = {
        id: 'step-2',
        application_id: 'app-123',
        step_name: 'Initial Review',
        step_order: 2,
        status: WorkflowStepStatus.Not_Started,
      };

      jest
        .spyOn(prismaService.application_step, 'findFirst')
        .mockResolvedValueOnce(currentStep as any)
        .mockResolvedValueOnce(nextStep as any);

      jest.spyOn(prismaService.application_step, 'update').mockResolvedValue({
        ...currentStep,
        status: WorkflowStepStatus.Completed,
        completed_at: new Date(),
      } as any);

      jest
        .spyOn(service, 'validateEnhancedStepCompletion' as any)
        .mockResolvedValue(true);

      const options = {
        evaluateBranching: true,
        checkParallelCompletion: true,
        triggerEscalation: true,
      };

      const result = await service.advanceEnhancedWorkflow(
        'app-123',
        1,
        { data: 'test' },
        options,
      );

      expect(result).toBeDefined();
      expect(prismaService.application_step.update).toHaveBeenCalled();
    });

    it('should handle conditional branching evaluation', async () => {
      const currentStep = {
        id: 'step-1',
        application_id: 'app-123',
        step_name: 'Document Collection',
        step_order: 1,
        status: WorkflowStepStatus.In_Progress,
        application: mockApplication,
        step_data: {
          branching_rules: [
            {
              condition: 'document_type === "urgent"',
              target_step: 3,
            },
          ],
        },
      };

      jest
        .spyOn(prismaService.application_step, 'findFirst')
        .mockResolvedValue(currentStep as any);
      jest
        .spyOn(service, 'validateEnhancedStepCompletion' as any)
        .mockResolvedValue(true);
      jest
        .spyOn(service, 'evaluateConditionalBranching' as any)
        .mockResolvedValue({
          id: 'step-3',
          step_order: 3,
        });

      const result = await service.advanceEnhancedWorkflow(
        'app-123',
        1,
        { document_type: 'urgent' },
        { evaluateBranching: true },
      );

      expect(result).toBeDefined();
    });
  });

  describe('migrateWorkflowVersion', () => {
    it('should throw error as workflow template functionality is removed', async () => {
      // REMOVED: workflow_template functionality
      // Test now verifies that the method throws an error indicating removal

      await expect(
        service.migrateWorkflowVersion(
          'app-123',
          'new-template-123',
          'preserve_progress',
        ),
      ).rejects.toThrow('Workflow template functionality has been removed');
    });

    it('should handle migration errors for removed functionality', async () => {
      await expect(
        service.migrateWorkflowVersion(
          'nonexistent',
          'new-template-123',
          'preserve_progress',
        ),
      ).rejects.toThrow('Workflow template functionality has been removed');
    });
  });

  describe('monitorSLAWithEscalation', () => {
    it('should monitor SLA and trigger escalations', async () => {
      const applicationsWithSLA = [
        {
          ...mockApplication,
          sla_deadline: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day overdue
          steps: [
            {
              id: 'step-1',
              status: WorkflowStepStatus.In_Progress,
              due_date: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours overdue
            },
          ],
        },
      ];

      const overdueSteps = [
        {
          id: 'step-1',
          application_id: 'app-123',
          step_name: 'Document Collection',
          status: WorkflowStepStatus.In_Progress,
          due_date: new Date(Date.now() - 12 * 60 * 60 * 1000),
          application: mockApplication,
        },
      ];

      jest
        .spyOn(prismaService.application, 'findMany')
        .mockResolvedValue(applicationsWithSLA as any);
      jest
        .spyOn(service, 'checkOverdueSteps')
        .mockResolvedValue(overdueSteps as any);
      jest
        .spyOn(service, 'evaluateApplicationSLA' as any)
        .mockResolvedValue(undefined);
      jest
        .spyOn(service, 'processStepEscalation' as any)
        .mockResolvedValue(undefined);

      await service.monitorSLAWithEscalation();

      expect(prismaService.application.findMany).toHaveBeenCalled();
      expect(service.checkOverdueSteps).toHaveBeenCalled();
    });
  });

  describe('generateWorkflowAnalytics', () => {
    it('should generate comprehensive analytics', async () => {
      jest
        .spyOn(prismaService.application, 'count')
        .mockResolvedValueOnce(100) // total applications
        .mockResolvedValueOnce(80); // completed applications

      jest
        .spyOn(service, 'calculateAverageCompletionTime' as any)
        .mockResolvedValue(120); // 120 hours
      jest
        .spyOn(service, 'calculateSLAComplianceRate' as any)
        .mockResolvedValue(85); // 85%
      jest
        .spyOn(service, 'identifyCommonBottlenecks' as any)
        .mockResolvedValue([
          { stepName: 'Document Review', averageDelay: 24, frequency: 15 },
        ]);
      jest
        .spyOn(service, 'generatePerformanceTrends' as any)
        .mockResolvedValue([
          { date: new Date(), completionRate: 80, averageDuration: 120 },
        ]);

      const analytics = await service.generateWorkflowAnalytics(
        'month',
        'immigration',
      );

      expect(analytics).toEqual({
        period: 'month',
        totalApplications: 100,
        completedApplications: 80,
        averageCompletionTime: 120,
        slaComplianceRate: 85,
        commonBottlenecks: [
          { stepName: 'Document Review', averageDelay: 24, frequency: 15 },
        ],
        performanceTrends: [
          { date: expect.any(Date), completionRate: 80, averageDuration: 120 },
        ],
      });
    });
  });

  describe('getWorkflowMetrics', () => {
    it('should return comprehensive workflow metrics', async () => {
      const applicationWithSteps = {
        ...mockApplication,
        steps: [
          {
            id: 'step-1',
            step_order: 1,
            status: WorkflowStepStatus.Completed,
            started_at: new Date(Date.now() - 48 * 60 * 60 * 1000),
            completed_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
          {
            id: 'step-2',
            step_order: 2,
            status: WorkflowStepStatus.In_Progress,
            started_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
            due_date: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour overdue
          },
        ],
      };

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(applicationWithSteps as any);

      const metrics = await service.getWorkflowMetrics('app-123');

      expect(metrics).toEqual({
        applicationId: 'app-123',
        totalSteps: 2,
        completedSteps: 1,
        averageStepDuration: expect.any(Number),
        totalDuration: expect.any(Number),
        slaCompliance: expect.any(Boolean),
        bottleneckSteps: expect.any(Array),
        performanceScore: expect.any(Number),
      });
    });

    it('should handle application not found', async () => {
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(null);

      await expect(service.getWorkflowMetrics('nonexistent')).rejects.toThrow(
        'Application not found: nonexistent',
      );
    });
  });

  describe('Performance Calculations', () => {
    it('should calculate average step duration correctly', () => {
      const steps = [
        {
          started_at: new Date('2024-01-01T00:00:00Z'),
          completed_at: new Date('2024-01-01T24:00:00Z'), // 24 hours
        },
        {
          started_at: new Date('2024-01-02T00:00:00Z'),
          completed_at: new Date('2024-01-02T12:00:00Z'), // 12 hours
        },
      ];

      const result = service['calculateAverageStepDuration'](steps);
      expect(result).toBe(18); // Average of 24 and 12 hours
    });

    it('should calculate performance score correctly', () => {
      const application = {
        sla_deadline: new Date(Date.now() + 24 * 60 * 60 * 1000), // Future deadline
        status: 'In_Progress',
        estimated_duration: 168,
        completed_at: null,
        steps: [],
      };

      const score = service['calculatePerformanceScore'](application);
      expect(score).toBe(70); // Score reduced due to long duration (100 - 30 for SLA compliance issue)
    });

    it('should identify bottleneck steps correctly', () => {
      const steps = [
        {
          step_name: 'Document Review',
          status: 'In_Progress',
          due_date: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day overdue
        },
        {
          step_name: 'Background Check',
          status: 'Completed',
          due_date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Future due date
        },
      ];

      const bottlenecks = service['identifyBottleneckSteps'](steps);
      expect(bottlenecks).toEqual(['Document Review']);
    });
  });
});
