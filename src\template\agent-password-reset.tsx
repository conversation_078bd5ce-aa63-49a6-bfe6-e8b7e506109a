import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface AgentPasswordResetEmailProps {
  agentName: string;
  resetUrl: string;
}

export default function AgentPasswordResetEmail({
  agentName = 'Agent',
  resetUrl = 'https://careerireland.com/agent/reset-password',
}: AgentPasswordResetEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>Reset your CareerIreland Agent password</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="CareerIreland Logo"
                style={logoImage}
              />
            </Link>
          </Container>

          <Heading style={h1}>Password Reset Request</Heading>

          <Text style={text}>Hi {agentName},</Text>

          <Text style={text}>
            We received a request to reset the password for your CareerIreland agent account. 
            If you made this request, click the button below to reset your password.
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={resetUrl}>
              Reset Password
            </Button>
          </Section>

          <Text style={text}>
            This password reset link will expire in <strong>15 minutes</strong> for security purposes.
          </Text>

          <Hr style={hr} />

          <Text style={securityText}>
            <strong>Security Notice:</strong>
          </Text>
          
          <Text style={text}>
            • If you didn't request this password reset, you can safely ignore this email
          </Text>
          <Text style={text}>
            • Your password will remain unchanged unless you click the reset link above
          </Text>
          <Text style={text}>
            • Never share your password or reset links with anyone
          </Text>
          <Text style={text}>
            • If you're having trouble, contact our support team immediately
          </Text>

          <Hr style={hr} />

          <Text style={footerText}>
            If the button above doesn't work, you can copy and paste this link into your browser:
          </Text>
          <Text style={linkText}>{resetUrl}</Text>

          <Text style={text}>
            Best regards,
            <br />
            The CareerIreland Security Team
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '0 48px',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '24px auto',
  padding: '48px 24px',
  borderRadius: '8px',
  maxWidth: '600px',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};

const h1 = {
  color: '#dc2626',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '1.3',
  margin: '0 0 32px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#dc2626',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '14px 28px',
  margin: '0 auto',
  transition: 'background-color 0.2s ease',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const securityText = {
  color: '#dc2626',
  fontSize: '18px',
  fontWeight: '600',
  margin: '24px 0 16px',
};

const footerText = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '24px 0 8px',
};

const linkText = {
  color: '#3b82f6',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0 0 24px',
  wordBreak: 'break-all' as const,
  fontFamily: 'monospace',
  backgroundColor: '#f3f4f6',
  padding: '8px',
  borderRadius: '4px',
};
