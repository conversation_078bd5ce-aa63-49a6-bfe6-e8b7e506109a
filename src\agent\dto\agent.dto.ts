import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AgentStatus } from '@prisma/client';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  Min<PERSON>ength,
  IsPhoneNumber,
  IsU<PERSON>D,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO for agent registration (admin-only)
 */
export class CreateAgentDto {
  @ApiProperty({
    description: 'Agent full name',
    example: '<PERSON>',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Agent email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Agent phone number',
    example: '+353-1-234-5678',
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Agent status',
    enum: AgentStatus,
    default: AgentStatus.Active,
  })
  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;
}

/**
 * DTO for agent login
 */
export class AgentLoginDto {
  @ApiProperty({
    description: 'Agent email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Agent password',
    example: 'SecurePassword123!',
  })
  @IsString()
  password: string;
}

/**
 * DTO for agent password update
 */
export class UpdateAgentPasswordDto {
  @ApiProperty({
    description: 'Current password',
    example: 'OldPassword123!',
  })
  @IsString()
  currentPassword: string;

  @ApiProperty({
    description: 'New password (minimum 8 characters)',
    example: 'NewSecurePassword123!',
  })
  @IsString()
  @MinLength(8)
  newPassword: string;
}

/**
 * DTO for agent password reset (without login)
 */
export class ResetAgentPasswordDto {
  @ApiProperty({
    description: 'Agent email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;
}

/**
 * DTO for agent password reset confirmation
 */
export class ConfirmResetAgentPasswordDto {
  @ApiProperty({
    description: 'Password reset token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'New password (minimum 8 characters)',
    example: 'NewSecurePassword123!',
  })
  @IsString()
  @MinLength(8)
  newPassword: string;
}

/**
 * DTO for updating agent details
 */
export class UpdateAgentDto {
  @ApiPropertyOptional({
    description: 'Agent full name',
    example: 'John Smith',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Agent email address',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'Agent phone number',
    example: '+353-1-234-5678',
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Agent status (admin only)',
    enum: AgentStatus,
  })
  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;
}

/**
 * DTO for agent query/filtering
 */
export class AgentQueryDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 1;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 1 : parsed;
  })
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 10;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 10 : parsed;
  })
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter by agent status',
    enum: AgentStatus,
  })
  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;

  @ApiPropertyOptional({
    description: 'Search by name or email',
    example: 'john',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

/**
 * DTO for agent response
 */
export class AgentResponseDto {
  @ApiProperty({ description: 'Agent ID' })
  id: string;

  @ApiProperty({ description: 'Agent full name' })
  name: string;

  @ApiProperty({ description: 'Agent email address' })
  email: string;

  @ApiPropertyOptional({ description: 'Agent phone number' })
  phone?: string;

  @ApiProperty({ description: 'Agent status', enum: AgentStatus })
  status: AgentStatus;

  @ApiProperty({ description: 'Creation date' })
  created_at: Date;

  @ApiProperty({ description: 'Last update date' })
  updated_at: Date;

  @ApiPropertyOptional({ description: 'Admin who created this agent' })
  created_by_admin?: {
    id: string;
    name: string;
    email: string;
  };
}

/**
 * DTO for agent list response
 */
export class AgentListResponseDto {
  @ApiProperty({ type: [AgentResponseDto] })
  data: AgentResponseDto[];

  @ApiProperty({ description: 'Total number of agents' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;
}

/**
 * DTO for agent authentication response
 */
export class AgentAuthResponseDto {
  @ApiProperty({ description: 'Agent information' })
  user: Omit<AgentResponseDto, 'created_by_admin'>;

  @ApiProperty({ description: 'Authentication tokens' })
  backendTokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}

/**
 * DTO for agent creation response
 */
export class AgentCreationResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Created agent information' })
  agent: AgentResponseDto;

  @ApiProperty({ description: 'Auto-generated password (sent via email)' })
  temporaryPassword: string;
}

/**
 * DTO for updating application priority (admin-only)
 */
export class UpdateApplicationPriorityDto {
  @ApiProperty({
    description: 'Priority level',
    enum: ['Low', 'Medium', 'High', 'Critical'],
    example: 'High',
  })
  @IsEnum(['Low', 'Medium', 'High', 'Critical'])
  priority_level: 'Low' | 'Medium' | 'High' | 'Critical';
}

/**
 * DTO for assigning application to agent
 */
export class AssignApplicationDto {
  @ApiProperty({
    description: 'Agent ID to assign the application to',
    example: 'agent_123456789',
  })
  @IsUUID()
  agentId: string;
}

/**
 * DTO for application priority update response
 */
export class PriorityUpdateResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Updated priority level' })
  priority_level: string;

  @ApiProperty({ description: 'Application ID' })
  applicationId: string;
}

/**
 * DTO for application assignment response
 */
export class AssignmentResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Assigned agent information' })
  assignedAgent: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({ description: 'Application ID' })
  applicationId: string;
}
