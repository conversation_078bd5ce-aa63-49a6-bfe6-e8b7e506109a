/**
 * Jest Configuration for User Module Tests
 *
 * Optimized configuration for running user registration and authentication tests.
 */

module.exports = {
  displayName: 'User Module Tests',
  testMatch: ['<rootDir>/test/user/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/user/**/*.(t|j)s',
    '!src/user/**/*.spec.ts',
    '!src/user/**/*.interface.ts',
    '!src/user/**/*.dto.ts',
  ],
  coverageDirectory: 'coverage/user',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  // setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  testTimeout: 30000,
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
};
