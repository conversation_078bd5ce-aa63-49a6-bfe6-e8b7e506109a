#!/usr/bin/env ts-node

/**
 * Test script for Task 2: Core Service Abstractions Implementation
 * This script validates the abstract services, interfaces, and concrete implementations
 *
 * Task 1: Database Schema Migration Implementation - COMPLETED ✅
 * Task 2: Core Service Abstractions Implementation - TESTING 🧪
 */

// Simple test without full NestJS compilation
import * as fs from 'fs';
import * as path from 'path';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  message?: string;
  error?: string;
}

const results: TestResult[] = [];

async function runTest(
  testName: string,
  testFn: () => Promise<void>,
): Promise<void> {
  try {
    await testFn();
    results.push({ test: testName, status: 'PASS' });
    console.log(`✅ ${testName}`);
  } catch (error) {
    results.push({
      test: testName,
      status: 'FAIL',
      error: error instanceof Error ? error.message : String(error),
    });
    console.log(
      `❌ ${testName}: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

// File paths to test
const testFiles = [
  'src/application/interfaces/application.interfaces.ts',
  'src/application/dto/application.dto.ts',
  'src/application/abstractions/abstract-application.service.ts',
  'src/application/abstractions/base-application.controller.ts',
  'src/application/services/workflow-engine.service.ts',
  'src/application/services/notification.service.ts',
  'src/application/implementations/immigration-application.service.ts',
  'src/application/application.module.ts',
  'src/application/README.md',
];

async function testFileExistence(): Promise<void> {
  for (const filePath of testFiles) {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
  }
}

async function testFileStructure(): Promise<void> {
  const requiredClasses = [
    'AbstractApplicationService',
    'BaseApplicationController',
    'WorkflowEngineService',
    'NotificationService',
    'ImmigrationApplicationService',
  ];

  for (const className of requiredClasses) {
    let found = false;
    for (const filePath of testFiles) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        if (
          content.includes(`class ${className}`) ||
          content.includes(`export class ${className}`)
        ) {
          found = true;
          break;
        }
      }
    }
    if (!found) {
      throw new Error(`Class ${className} not found in any file`);
    }
  }
}

async function testInterfaceDefinitions(): Promise<void> {
  const interfacePath = 'src/application/interfaces/application.interfaces.ts';
  if (!fs.existsSync(interfacePath)) {
    throw new Error('Interface file not found');
  }

  const content = fs.readFileSync(interfacePath, 'utf8');
  const requiredInterfaces = [
    'IApplication',
    'IWorkflowTemplate',
    'IApplicationStep',
    'INotification',
    'IAbstractApplicationService',
  ];

  for (const interfaceName of requiredInterfaces) {
    if (!content.includes(`interface ${interfaceName}`)) {
      throw new Error(`Interface ${interfaceName} not found`);
    }
  }
}

async function testDTODefinitions(): Promise<void> {
  const dtoPath = 'src/application/dto/application.dto.ts';
  if (!fs.existsSync(dtoPath)) {
    throw new Error('DTO file not found');
  }

  const content = fs.readFileSync(dtoPath, 'utf8');
  const requiredDTOs = [
    'CreateApplicationDto',
    'UpdateApplicationStatusDto',
    'ApplicationResponseDto',
  ];

  for (const dtoName of requiredDTOs) {
    if (!content.includes(`class ${dtoName}`)) {
      throw new Error(`DTO ${dtoName} not found`);
    }
  }
}

async function testModuleConfiguration(): Promise<void> {
  const modulePath = 'src/application/application.module.ts';
  if (!fs.existsSync(modulePath)) {
    throw new Error('Application module not found');
  }

  const content = fs.readFileSync(modulePath, 'utf8');

  if (!content.includes('@Module')) {
    throw new Error('Module decorator not found');
  }

  if (!content.includes('WorkflowEngineService')) {
    throw new Error('WorkflowEngineService not configured in module');
  }

  if (!content.includes('NotificationService')) {
    throw new Error('NotificationService not configured in module');
  }
}

async function testAbstractImplementation(): Promise<void> {
  const abstractPath =
    'src/application/abstractions/abstract-application.service.ts';
  const concretePath =
    'src/application/implementations/immigration-application.service.ts';

  if (!fs.existsSync(abstractPath) || !fs.existsSync(concretePath)) {
    throw new Error('Abstract or concrete implementation files not found');
  }

  const abstractContent = fs.readFileSync(abstractPath, 'utf8');
  const concreteContent = fs.readFileSync(concretePath, 'utf8');

  // Check abstract class has abstract methods
  const abstractMethods = [
    'generateApplicationNumber',
    'transformApplicationDetails',
    'getServiceSpecificData',
    'validateApplicationRequirements',
  ];

  for (const method of abstractMethods) {
    if (
      !abstractContent.includes(`abstract ${method}`) &&
      !abstractContent.includes(`abstract async ${method}`)
    ) {
      throw new Error(`Abstract method ${method} not found in abstract class`);
    }
  }

  // Check concrete class extends abstract and implements methods
  if (!concreteContent.includes('extends AbstractApplicationService')) {
    throw new Error(
      'Concrete class does not extend AbstractApplicationService',
    );
  }

  for (const method of abstractMethods) {
    if (!concreteContent.includes(`async ${method}(`)) {
      throw new Error(`Method ${method} not implemented in concrete class`);
    }
  }
}

async function main(): Promise<void> {
  console.log('🧪 Testing Task 2: Core Service Abstractions Implementation\n');

  await runTest('File Existence', testFileExistence);
  await runTest('File Structure', testFileStructure);
  await runTest('Interface Definitions', testInterfaceDefinitions);
  await runTest('DTO Definitions', testDTODefinitions);
  await runTest('Module Configuration', testModuleConfiguration);
  await runTest('Abstract Implementation', testAbstractImplementation);

  console.log('\n📊 Test Results Summary:');
  console.log('========================');

  const passCount = results.filter((r) => r.status === 'PASS').length;
  const failCount = results.filter((r) => r.status === 'FAIL').length;

  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(
    `📈 Success Rate: ${((passCount / results.length) * 100).toFixed(1)}%`,
  );

  if (failCount > 0) {
    console.log('\n❌ Failed Tests:');
    results
      .filter((r) => r.status === 'FAIL')
      .forEach((result) => {
        console.log(`   - ${result.test}: ${result.error}`);
      });

    console.log('\n🔧 Task 2 Implementation Status: NEEDS FIXES');
    console.log(
      'Please review and fix the failing tests before proceeding to Task 3.',
    );
    process.exit(1);
  } else {
    console.log(
      '\n🎉 All tests passed! Task 2 implementation is working correctly.',
    );
    console.log(
      '✅ Task 2: Core Service Abstractions Implementation - COMPLETED',
    );
    console.log(
      '\n🚀 Ready to proceed with Task 3: Workflow Engine Implementation',
    );
  }
}

main().catch((error) => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
