-- Rollback script for Dynamic Workflow System
-- This script safely removes all tables and enums created by the migration
-- Run this script if you need to rollback the migration

-- WARNING: This will permanently delete all data in the new tables
-- Make sure to backup your data before running this script

BEGIN;

-- Drop foreign key constraints first
ALTER TABLE "application" DROP CONSTRAINT IF EXISTS "application_user_id_fkey";
ALTER TABLE "application" DROP CONSTRAINT IF EXISTS "application_service_id_fkey";
ALTER TABLE "application" DROP CONSTRAINT IF EXISTS "application_workflow_template_id_fkey";
ALTER TABLE "application" DROP CONSTRAINT IF EXISTS "application_current_step_id_fkey";
ALTER TABLE "application_step" DROP CONSTRAINT IF EXISTS "application_step_application_id_fkey";
ALTER TABLE "document_vault" DROP CONSTRAINT IF EXISTS "document_vault_user_id_fkey";
ALTER TABLE "document_vault" DROP CONSTRAINT IF EXISTS "document_vault_parent_document_id_fkey";
ALTER TABLE "application_document" DROP CONSTRAINT IF EXISTS "application_document_application_id_fkey";
ALTER TABLE "application_document" DROP CONSTRAINT IF EXISTS "application_document_document_vault_id_fkey";
ALTER TABLE "notification_queue" DROP CONSTRAINT IF EXISTS "notification_queue_template_id_fkey";
ALTER TABLE "notification_queue" DROP CONSTRAINT IF EXISTS "notification_queue_recipient_user_id_fkey";
ALTER TABLE "notification_queue" DROP CONSTRAINT IF EXISTS "notification_queue_application_id_fkey";

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS "notification_queue";
DROP TABLE IF EXISTS "notification_template";
DROP TABLE IF EXISTS "application_document";
DROP TABLE IF EXISTS "document_vault";
DROP TABLE IF EXISTS "application_step";
DROP TABLE IF EXISTS "application";
DROP TABLE IF EXISTS "workflow_template";

-- Drop enums
DROP TYPE IF EXISTS "PriorityLevel";
DROP TYPE IF EXISTS "NotificationStatus";
DROP TYPE IF EXISTS "NotificationChannel";
DROP TYPE IF EXISTS "DocumentType";
DROP TYPE IF EXISTS "DocumentStatus";
DROP TYPE IF EXISTS "WorkflowStepStatus";
DROP TYPE IF EXISTS "ApplicationStatus";

COMMIT;

-- Verify rollback completed successfully
SELECT 'Rollback completed successfully' AS status;
