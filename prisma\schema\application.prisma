// Core Application Management Tables for Dynamic Workflow System

model application {
  id                 String  @id @default(cuid())
  application_number String  @unique // Auto-generated unique identifier
  service_type       String // Links to existing service types (e.g., "immigration")
  service_id         String? // Polymorphic reference to service/package/training/consulting

  // User Information (polymorphic - can be user or guest)
  user_id      String?
  guest_name   String?
  guest_email  String?
  guest_mobile String?

  // Application Status and Workflow
  status               ApplicationStatus @default(Draft)
  current_step         String @default("1") // Current workflow step (changed from Int to String)
  workflow_template_id String?

  priority_level       PriorityLevel @default(Medium)
  estimated_completion DateTime?

  // Payment Integration - Support multiple payments
  payment_ids          String[] @default([]) // Array of payment IDs

  // Agent Assignment - Support multiple agents
  agent_ids            String[] @default([]) // Array of agent IDs

  // Notes and Comments
  note         String?   // Application notes field for internal use

  // Audit Fields
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  completed_at DateTime? // When application was completed
  created_by   String?   // Admin/Agent who created

  // Relationships
  user              user?              @relation(fields: [user_id], references: [id])
  workflow_template workflow_template? @relation(fields: [workflow_template_id], references: [id])

  // Note: Removed direct foreign key relationships for arrays
  // payments and agents will be resolved programmatically

  steps         Json
  form_data     application_form[]
  documents     application_document[]
  notifications notification_queue[]

  @@index([application_number])
  @@index([service_type])
  @@index([status])
  @@index([user_id])
  @@index([guest_email])
  @@index([created_at])
  @@index([payment_ids])
  @@index([agent_ids])
  @@map("application")
}


// NEW: application_form table for dynamic workflow form data storage
model application_form {
  id             String @id @default(cuid())
  application_id String
  stage_order    Int
  field_name     String
  field_type     String
  required       Boolean @default(false)
  field_value    String?
  field_options  Json? // JSONB for select/radio field options
  show_to_client Boolean @default(true)

  // Audit Fields
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  application application @relation(fields: [application_id], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([application_id])
  @@index([stage_order])
  @@index([field_name])
  @@index([created_at])

  // Unique constraint to prevent duplicate fields per stage
  @@unique([application_id, stage_order, field_name], name: "application_form_unique_field")
  @@map("application_form")
}
