import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseClient, createClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private readonly logger = new Logger(SupabaseService.name);
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    this.initializeSupabaseClient();
  }

  private initializeSupabaseClient() {
    const SUPABASE_URL = process.env.SUPABASE_URL;
    const SUPABASE_KEY = process.env.SUPABASE_KEY;

    // Validate configuration
    if (!SUPABASE_URL || !SUPABASE_KEY) {
      this.logger.error('Missing Supabase configuration: SUPABASE_URL or SUPABASE_KEY not set');
      throw new Error('Supabase configuration is incomplete');
    }

    // Validate URL format
    try {
      new URL(SUPABASE_URL);
    } catch (error) {
      this.logger.error(`Invalid SUPABASE_URL format: ${SUPABASE_URL}`);
      throw new Error('Invalid Supabase URL format');
    }

    this.logger.log(`Initializing Supabase client with URL: ${SUPABASE_URL}`);

    try {
      this.supabase = createClient(SUPABASE_URL, SUPABASE_KEY, {
        auth: {
          persistSession: false,
        },
        global: {
          fetch: this.createFetchWithRetry(),
        },
      });
      this.logger.log('Supabase client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Supabase client', error);
      throw new Error('Failed to initialize Supabase client');
    }
  }

  private createFetchWithRetry() {
    return async (url: RequestInfo | URL, options?: RequestInit): Promise<Response> => {
      const maxRetries = 3;
      const retryDelay = 1000; // 1 second

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          this.logger.debug(`Fetch attempt ${attempt} to ${url}`);

          // Add timeout to the request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const fetchOptions = {
            ...options,
            signal: controller.signal,
          };

          const response = await fetch(url, fetchOptions);
          clearTimeout(timeoutId);

          if (!response.ok) {
            this.logger.warn(`HTTP ${response.status} ${response.statusText} for ${url}`);
          }

          return response;
        } catch (error) {
          this.logger.error(`Fetch attempt ${attempt} failed for ${url}:`, error);

          if (attempt === maxRetries) {
            // On final attempt, throw a more descriptive error
            if (error.name === 'AbortError') {
              throw new Error(`Request timeout: Failed to connect to Supabase after 30 seconds`);
            } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
              throw new Error(`Network error: Cannot reach Supabase server at ${url}`);
            } else {
              throw new Error(`Fetch failed after ${maxRetries} attempts: ${error.message}`);
            }
          }

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }
      }
    };
  }

  getClient() {
    if (!this.supabase) {
      throw new Error('Supabase client not initialized');
    }
    return this.supabase;
  }

  /**
   * Test the connection to Supabase
   */
  async testConnection(): Promise<boolean> {
    try {
      this.logger.log('Testing Supabase connection...');
      const { data, error } = await this.supabase.storage.listBuckets();

      if (error) {
        this.logger.error('Supabase connection test failed:', error);
        return false;
      }

      this.logger.log('Supabase connection test successful');
      return true;
    } catch (error) {
      this.logger.error('Supabase connection test failed with exception:', error);
      return false;
    }
  }
}
