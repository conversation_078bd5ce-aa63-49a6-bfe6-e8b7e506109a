/**
 * Document Vault DTOs
 *
 * Data transfer objects for document vault management functionality.
 * Provides validation and structure for document vault operations.
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, Min, Max, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';
import { DocumentType } from '@prisma/client';

/**
 * Document Vault Query DTO
 */
export class DocumentVaultQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by document type',
    enum: DocumentType,
  })
  @IsOptional()
  @IsEnum(DocumentType)
  document_type?: DocumentType;

  @ApiPropertyOptional({
    description: 'Search in document names and content',
    example: 'passport',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 1;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 1 : parsed;
  })
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 20;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 20 : parsed;
  })
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;
}

/**
 * Document Vault Item DTO
 */
export class DocumentVaultItemDto {
  @ApiProperty({
    description: 'Document ID',
    example: 'doc_123456789',
  })
  id: string;

  @ApiProperty({
    description: 'Document name',
    example: 'Passport Copy',
  })
  document_name: string;

  @ApiProperty({
    description: 'Original filename',
    example: 'passport_john_doe.pdf',
  })
  original_filename: string;

  @ApiProperty({
    description: 'Document type',
    example: 'Passport',
  })
  document_type: string;

  @ApiPropertyOptional({
    description: 'Document category',
    example: 'identity_documents',
  })
  document_category?: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 2048576,
  })
  file_size: number;

  @ApiPropertyOptional({
    description: 'Document expiry date',
    example: '2030-12-31T00:00:00.000Z',
  })
  expiry_date?: Date;

  @ApiProperty({
    description: 'Upload date',
    example: '2025-06-23T07:12:04.000Z',
  })
  uploaded_at: Date;

  @ApiProperty({
    description: 'Creation date',
    example: '2025-06-23T07:12:04.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2025-06-23T07:12:04.000Z',
  })
  updated_at: Date;
}

/**
 * Document Vault Response DTO
 */
export class DocumentVaultResponseDto {
  @ApiProperty({
    description: 'Operation status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'List of documents',
    type: [DocumentVaultItemDto],
  })
  data: DocumentVaultItemDto[];

  @ApiProperty({
    description: 'Pagination information',
  })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
