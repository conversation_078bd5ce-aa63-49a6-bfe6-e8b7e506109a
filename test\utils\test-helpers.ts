/**
 * Test Helpers and Utilities
 *
 * Shared utilities, mocks, and helper functions for payment module tests.
 * Provides consistent test setup and mock implementations across all test files.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../../src/utils/prisma.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import { STRIPE_CLIENT } from '../../src/config/stripe.config';
import Stripe from 'stripe';

/**
 * JWT Payload Interface for Testing
 */
export interface IJWTPayload {
  id: string;
  email: string;
  sub: {
    name: string;
  };
  iat: number;
  exp: number;
}

/**
 * Mock JWT Payload for testing authenticated users
 */
export const mockJWTPayload: IJWTPayload = {
  id: 'user_test_123',
  email: '<EMAIL>',
  sub: {
    name: 'Test User',
  },
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600,
};

/**
 * Mock Stripe Client
 * Provides mock implementations for Stripe operations
 * Enhanced with payment methods support for testing new fields
 */
export const mockStripeClient = {
  checkout: {
    sessions: {
      create: jest.fn(),
      retrieve: jest.fn(),
    },
  },
  webhooks: {
    constructEvent: jest.fn(),
  },
  paymentIntents: {
    retrieve: jest.fn(),
  },
  paymentMethods: {
    retrieve: jest.fn(),
  },
};

/**
 * Mock Prisma Service
 * Provides mock implementations for database operations
 */
export const mockPrismaService = {
  $transaction: jest.fn(),
  payment: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    aggregate: jest.fn(),
    groupBy: jest.fn(),
  },
  service: {
    findUnique: jest.fn(),
  },
  packages: {
    findUnique: jest.fn(),
  },
  immigration_service: {
    findUnique: jest.fn(),
  },
  training: {
    findUnique: jest.fn(),
  },
  user: {
    findUnique: jest.fn(),
  },
};

/**
 * Mock Mailer Service
 * Provides mock implementations for email operations
 */
export const mockMailerService = {
  sendMail: jest.fn(),
  sendEmail: jest.fn(),
};

/**
 * Mock JWT Service
 * Provides mock implementations for JWT operations
 */
export const mockJwtService = {
  sign: jest.fn(),
  verify: jest.fn(),
  decode: jest.fn(),
};

/**
 * Mock Logger Service
 * Provides mock implementations for logging operations
 */
export const mockLoggerService = {
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
  logPayment: jest.fn(),
  logRequest: jest.fn(),
  logWithCorrelation: jest.fn(),
  getLogger: jest.fn(),
};

/**
 * Create Testing Module with Common Mocks
 * Helper function to create a testing module with all common mocks configured
 */
export async function createTestingModuleWithMocks(
  providers: any[] = [],
): Promise<TestingModule> {
  return Test.createTestingModule({
    providers: [
      ...providers,
      {
        provide: PrismaService,
        useValue: mockPrismaService,
      },
      {
        provide: MailerService,
        useValue: mockMailerService,
      },
      {
        provide: JwtService,
        useValue: mockJwtService,
      },
      {
        provide: LoggerService,
        useValue: mockLoggerService,
      },
      {
        provide: STRIPE_CLIENT,
        useValue: mockStripeClient,
      },
    ],
  }).compile();
}

/**
 * Reset All Mocks
 * Helper function to reset all mock implementations between tests
 */
export function resetAllMocks(): void {
  jest.clearAllMocks();

  // Reset Stripe mocks
  Object.values(mockStripeClient.checkout.sessions).forEach((mock: any) =>
    mock.mockReset(),
  );
  Object.values(mockStripeClient.webhooks).forEach((mock: any) =>
    mock.mockReset(),
  );
  Object.values(mockStripeClient.paymentIntents).forEach((mock: any) =>
    mock.mockReset(),
  );
  Object.values(mockStripeClient.paymentMethods).forEach((mock: any) =>
    mock.mockReset(),
  );

  // Reset Prisma mocks
  mockPrismaService.$transaction.mockReset();
  Object.values(mockPrismaService.payment).forEach((mock: any) =>
    mock.mockReset(),
  );
  Object.entries(mockPrismaService).forEach(([key, value]) => {
    if (key === '$transaction') {
      // Skip $transaction as it's handled above
      return;
    }
    if (typeof value === 'object' && value !== null) {
      Object.values(value).forEach((mock: any) => {
        if (typeof mock?.mockReset === 'function') {
          mock.mockReset();
        }
      });
    }
  });

  // Reset other service mocks
  Object.values(mockMailerService).forEach((mock: any) => mock.mockReset());
  Object.values(mockJwtService).forEach((mock: any) => mock.mockReset());
  Object.values(mockLoggerService).forEach((mock: any) => mock.mockReset());
}

/**
 * Mock Stripe Checkout Session Response
 */
export const mockStripeSession: Stripe.Checkout.Session = {
  id: 'cs_test_123',
  object: 'checkout.session',
  url: 'https://checkout.stripe.com/pay/cs_test_123',
  payment_intent: 'pi_test_123',
  payment_status: 'unpaid',
  status: 'open',
  mode: 'payment',
  currency: 'eur',
  amount_total: 10000,
  metadata: {},
  created: Math.floor(Date.now() / 1000),
  expires_at: Math.floor(Date.now() / 1000) + 3600,
  livemode: false,
} as Stripe.Checkout.Session;

/**
 * Mock Stripe Payment Intent Response
 */
export const mockStripePaymentIntent: Stripe.PaymentIntent = {
  id: 'pi_test_123',
  object: 'payment_intent',
  status: 'succeeded',
  amount: 10000,
  currency: 'eur',
  metadata: {},
  created: Math.floor(Date.now() / 1000),
  livemode: false,
} as Stripe.PaymentIntent;

/**
 * Mock Database Payment Record
 * Enhanced with payment method and transaction ID fields
 */
export const mockPaymentRecord = {
  id: 'payment_test_123',
  amount: 10000,
  status: 'paid',
  payment_type: 'user',
  service_type: 'service',
  progress: 'Pending',
  userId: 'user_test_123',
  serviceId: 'service_test_123',
  packageId: null,
  immigration_serviceId: null,
  trainingId: null,
  guest_name: null,
  guest_email: null,
  guest_mobile: null,
  stripe_session_id: 'cs_test_123',
  stripe_payment_intent_id: 'pi_test_123',
  payment_method: 'card',
  transaction_id: 'ch_test_123',
  createdAt: new Date(),
  updatedAt: new Date(),
};

/**
 * Mock Service Data
 */
export const mockServiceData = {
  id: 'service_test_123',
  name: 'Test Service',
  description: 'Test service description',
  amount: 100,
  meeting_link: 'https://meet.example.com/test',
  mentor: 'Test Mentor', // This will be the normalized mentor name
};

/**
 * Mock Service Data with Mentor Relationship (as returned from database)
 */
export const mockServiceDataWithMentorRelation = {
  id: 'service_test_123',
  name: 'Test Service',
  description: 'Test service description',
  price: 100,
  meeting_link: 'https://meet.example.com/test',
  mentorId: 'mentor_test_123',
  mentor: {
    id: 'mentor_test_123',
    name: 'Test Mentor',
  },
};

/**
 * Mock Package Data
 */
export const mockPackageData = {
  id: 'package_test_123',
  name: 'Test Package',
  description: 'Test package description',
  amount: 200,
};

/**
 * Mock Immigration Service Data
 */
export const mockImmigrationServiceData = {
  id: 'immigration_test_123',
  name: 'Test Immigration Service',
  description: 'Test immigration service description',
  amount: 300,
};

/**
 * Mock Training Data
 */
export const mockTrainingData = {
  id: 'training_test_123',
  name: 'Test Training',
  description: 'Test training description',
  amount: 150,
};
