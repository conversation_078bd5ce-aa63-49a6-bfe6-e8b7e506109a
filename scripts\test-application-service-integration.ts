/**
 * Test Script for ApplicationService Integration
 *
 * Tests the enhanced ApplicationService.createApplicationFromPayment method
 * to ensure it properly integrates with the new ApplicationDocumentService.
 */

import { PrismaClient } from '@prisma/client';
import { ApplicationService } from '../src/application/application.service';
import { ApplicationFormService } from '../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../src/application/services/application-transformer.service';
import { MediaService } from '../src/media/media.service';
import { DocumentVaultService } from '../src/application/services/document-vault.service';
import { SupabaseService } from '../src/utils/supabase.service';
import { PrismaService } from '../src/utils/prisma.service';

const prisma = new PrismaClient();

async function testApplicationServiceIntegration() {
  console.log('🧪 Testing ApplicationService Integration...\n');

  try {
    // Initialize services (simulating dependency injection)
    const prismaService = new PrismaService();
    const formService = new ApplicationFormService(prismaService);
    const documentService = new ApplicationDocumentService(prismaService);
    const transformerService = new ApplicationTransformerService();
    const supabaseService = new SupabaseService({} as any); // Mock config service
    const mediaService = new MediaService(supabaseService);
    const documentVaultService = new DocumentVaultService(
      prismaService,
      mediaService,
    );
    // Mock NotificationService for testing
    const mockNotificationService = {
      sendNotification: jest.fn().mockResolvedValue({}),
    } as any;

    const applicationService = new ApplicationService(
      prismaService,
      formService,
      documentService,
      transformerService,
      mediaService,
      documentVaultService,
      mockNotificationService,
    );

    // 1. Create a test workflow template
    console.log('1. Creating test workflow template...');
    const workflowTemplate = await prisma.workflow_template.create({
      data: {
        name: 'Service Integration Test Workflow',
        description: 'Test workflow for service integration',
        serviceType: 'immigration',
        serviceId: 'service-integration-test',
        isActive: true,
        workflowTemplate: [
          {
            stageOrder: 1,
            stageName: 'Initial Documents',
            documentsRequired: true,
            customFormRequired: true,
            documents: [
              { documentName: 'passport', required: true },
              { documentName: 'application_form', required: true },
            ],
            customForm: [
              { fieldName: 'fullName', fieldType: 'text', required: true },
              { fieldName: 'nationality', fieldType: 'text', required: true },
            ],
          },
          {
            stageOrder: 2,
            stageName: 'Supporting Documents',
            documentsRequired: true,
            customFormRequired: false,
            documents: [
              { documentName: 'bank_statement', required: true },
              { documentName: 'employment_letter', required: false },
            ],
          },
        ],
        createdBy: 'test-admin',
      },
    });
    console.log(`✅ Created workflow template: ${workflowTemplate.id}`);

    // 2. Create a test user
    console.log('\n2. Creating test user...');
    const testUser = await prisma.user.create({
      data: {
        name: 'Integration Test User',
        email: `integration-test-${Date.now()}@example.com`,
        provider: 'credentials',
        emailVerified: true,
      },
    });
    console.log(`✅ Created test user: ${testUser.email}`);

    // 3. Create a test payment
    console.log('\n3. Creating test payment...');
    const testPayment = await prisma.payment.create({
      data: {
        amount: 25000, // €250.00
        status: 'paid',
        payment_type: 'user',
        service_type: 'immigration',
        progress: 'Completed',
        userId: testUser.id,
        stripe_session_id: `cs_integration_${Date.now()}`,
        stripe_payment_intent_id: `pi_integration_${Date.now()}`,
        payment_method: 'card',
        transaction_id: `txn_integration_${Date.now()}`,
      },
    });
    console.log(`✅ Created test payment: ${testPayment.id}`);

    // 4. Test ApplicationService.createApplicationFromPayment
    console.log(
      '\n4. Testing ApplicationService.createApplicationFromPayment...',
    );
    const applicationData = {
      paymentId: testPayment.id,
      serviceType: 'immigration',
      serviceId: 'service-integration-test',
      workflowTemplateId: workflowTemplate.id,
      userId: testUser.id,
    };

    const application =
      await applicationService.createApplicationFromPayment(applicationData);
    console.log(
      `✅ Created application via service: ${application.application_number}`,
    );

    // 5. Verify form data was populated
    console.log('\n5. Verifying form data population...');
    const formData = await prisma.application_form.findMany({
      where: { application_id: application.id },
      orderBy: [{ stage_order: 'asc' }, { field_name: 'asc' }],
    });
    console.log(`✅ Form fields populated: ${formData.length}`);

    // Log form field details
    formData.forEach((field) => {
      console.log(
        `   - Stage ${field.stage_order}: ${field.field_name} (${field.field_type}) ${field.required ? '[Required]' : '[Optional]'}`,
      );
    });

    // 6. Verify document requirements were populated
    console.log('\n6. Verifying document requirements population...');
    const documentRequirements = await prisma.application_document.findMany({
      where: { application_id: application.id },
      include: {
        document: {
          select: {
            document_name: true,
            document_type: true,
            document_category: true,
          },
        },
      },
      orderBy: [{ stage_order: 'asc' }, { file_name: 'asc' }],
    });
    console.log(
      `✅ Document requirements populated: ${documentRequirements.length}`,
    );

    // Log document requirement details
    documentRequirements.forEach((doc) => {
      console.log(
        `   - Stage ${doc.stage_order}: ${doc.file_name} ${doc.required ? '[Required]' : '[Optional]'} - Status: ${doc.status}`,
      );
    });

    // 7. Test ApplicationService.getApplicationById
    console.log('\n7. Testing ApplicationService.getApplicationById...');
    const applicationDetails = await applicationService.getApplicationById(
      application.id,
    );
    console.log(`✅ Retrieved application details successfully`);
    console.log(
      `   - Application Number: ${applicationDetails.applicationNumber}`,
    );
    console.log(`   - Status: ${applicationDetails.status}`);
    console.log(
      `   - Workflow Stages: ${applicationDetails.workflowStages?.length || 0}`,
    );

    // 8. Verify data integrity
    console.log('\n8. Verifying data integrity...');

    // Check that workflow template stages match populated data
    const workflowData = workflowTemplate.workflowTemplate as any[];
    const expectedFormFields = workflowData.reduce((count, stage) => {
      return count + (stage.customForm ? stage.customForm.length : 0);
    }, 0);
    const expectedDocuments = workflowData.reduce((count, stage) => {
      return count + (stage.documents ? stage.documents.length : 0);
    }, 0);

    console.log(
      `✅ Expected form fields: ${expectedFormFields}, Actual: ${formData.length}`,
    );
    console.log(
      `✅ Expected documents: ${expectedDocuments}, Actual: ${documentRequirements.length}`,
    );

    const formIntegrityCheck = formData.length === expectedFormFields;
    const documentIntegrityCheck =
      documentRequirements.length === expectedDocuments;

    console.log(
      `✅ Form data integrity: ${formIntegrityCheck ? 'PASS' : 'FAIL'}`,
    );
    console.log(
      `✅ Document data integrity: ${documentIntegrityCheck ? 'PASS' : 'FAIL'}`,
    );

    // 9. Test error handling (optional workflow template)
    console.log(
      '\n9. Testing application creation without workflow template...',
    );
    const applicationDataNoTemplate = {
      paymentId: testPayment.id,
      serviceType: 'immigration',
      serviceId: 'service-integration-test',
      // workflowTemplateId: undefined, // No template
      userId: testUser.id,
    };

    const applicationNoTemplate =
      await applicationService.createApplicationFromPayment(
        applicationDataNoTemplate,
      );
    console.log(
      `✅ Created application without template: ${applicationNoTemplate.application_number}`,
    );

    // Verify no form/document data was created for application without template
    const noTemplateFormData = await prisma.application_form.count({
      where: { application_id: applicationNoTemplate.id },
    });
    const noTemplateDocData = await prisma.application_document.count({
      where: { application_id: applicationNoTemplate.id },
    });

    console.log(
      `✅ No template - Form fields: ${noTemplateFormData}, Documents: ${noTemplateDocData}`,
    );

    // 10. Cleanup
    console.log('\n10. Cleaning up test data...');

    // Clean up applications and related data
    await prisma.application_document.deleteMany({
      where: {
        OR: [
          { application_id: application.id },
          { application_id: applicationNoTemplate.id },
        ],
      },
    });
    await prisma.application_form.deleteMany({
      where: {
        OR: [
          { application_id: application.id },
          { application_id: applicationNoTemplate.id },
        ],
      },
    });
    await prisma.document_vault.deleteMany({
      where: { document_category: 'workflow_requirement' },
    });
    await prisma.application.deleteMany({
      where: {
        id: {
          in: [application.id, applicationNoTemplate.id],
        },
      },
    });
    await prisma.payment.delete({
      where: { id: testPayment.id },
    });
    await prisma.workflow_template.delete({
      where: { id: workflowTemplate.id },
    });
    await prisma.user.delete({
      where: { id: testUser.id },
    });

    console.log('✅ Cleanup completed');

    console.log(
      '\n🎉 ApplicationService integration test passed successfully!',
    );
    console.log('\n📋 Summary:');
    console.log(
      `   - Form data integrity: ${formIntegrityCheck ? 'PASS' : 'FAIL'}`,
    );
    console.log(
      `   - Document data integrity: ${documentIntegrityCheck ? 'PASS' : 'FAIL'}`,
    );
    console.log(`   - Error handling: ✅ Working`);
    console.log(`   - Service integration: ✅ Complete`);
  } catch (error) {
    console.error('❌ ApplicationService integration test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testApplicationServiceIntegration().catch((error) => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
