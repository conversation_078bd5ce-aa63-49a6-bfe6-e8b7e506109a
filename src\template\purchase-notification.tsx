import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
} from '@react-email/components';
import { format } from 'date-fns';
import * as React from 'react';
// Define the type for the purchase data
type PurchaseData = {
  name: string;
  service: {
    id: string;
    name: string;
    mentor?: string;
    createdAt: Date;
    status: string;
    amount: number;
  };
  user: {
    email: string;
    name: string;
    mobile_no?: string;
  };
};

// Status badge color helper
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
      return '#10B981'; // green
    case 'pending':
      return '#F59E0B'; // amber
    case 'cancelled':
    case 'canceled':
      return '#EF4444'; // red
    default:
      return '#6B7280'; // gray
  }
};

export default function PurchaseNotificationEmail({
  name = 'Service',
  service = {
    id: '',
    createdAt: new Date(),
    status: 'Paid',
    amount: 0,
    name: 'Mastering the Art of Selling',
  },
  user = {
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON>',
  },
}: PurchaseData) {
  // Format the date
  const formattedDate = format(
    new Date(service.createdAt),
    "MMM d, yyyy 'at' h:mm a",
  );

  return (
    <Html>
      <Head />
      <Preview>
        New Purchase: {service.name} by {user.name}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              {name} Purchase Notification
            </Heading>
            <Text style={styles.paragraph}>
              A new purchase has been made on your platform. Here are the
              details:
            </Text>

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Service Details
              </Heading>
              {service.mentor && (
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Mentor Name:</strong> {service.mentor}
                  </Text>
                </Row>
              )}
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Service Name:</strong> {service.name}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Amount:</strong>{' '}
                  {new Intl.NumberFormat('en-IE', {
                    style: 'currency',
                    currency: 'EUR',
                  }).format(service?.amount || 0)}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Status:</strong>{' '}
                  <span
                    style={{
                      ...styles.value,
                      color: getStatusColor(service.status),
                      fontWeight: 'bold',
                    }}
                  >
                    {service.status.toUpperCase()}
                  </span>
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Purchase Date:</strong> {formattedDate}
                </Text>
              </Row>
            </Section>

            <Hr style={styles.divider} />

            <Section style={styles.card}>
              <Heading as="h2" style={styles.subheading}>
                Customer Information
              </Heading>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Name:</strong> {user.name}
                </Text>
              </Row>
              <Row style={styles.row}>
                <Text style={styles.label}>
                  <strong>Email:</strong>{' '}
                  <a href={`mailto:${user.email}`} style={styles.link}>
                    {user.email}
                  </a>
                </Text>
              </Row>
              {user?.mobile_no && (
                <Row style={styles.row}>
                  <Text style={styles.label}>
                    <strong>Mobile No:</strong> {user.mobile_no}
                  </Text>
                </Row>
              )}
            </Section>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Careerireland all rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  header: {
    padding: '20px',
    textAlign: 'center' as const,
  },
  main: {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    padding: '40px 20px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  },
  heading: {
    fontSize: '24px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#333',
    textAlign: 'center' as const,
    margin: '0 0 24px',
  },
  subheading: {
    fontSize: '18px',
    lineHeight: '1.3',
    fontWeight: '600',
    color: '#333',
    margin: '0 0 16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.5',
    color: '#4a5568',
    margin: '0 0 24px',
  },
  card: {
    backgroundColor: '#f9fafb',
    borderRadius: '6px',
    padding: '20px',
    marginBottom: '24px',
  },
  row: {
    marginBottom: '8px',
  },
  label: {
    fontSize: '14px',
    color: '#000',
    margin: '0',
  },
  value: {
    fontSize: '14px',
    color: '#2d3748',
    fontWeight: '500',
    margin: '0',
  },
  divider: {
    borderColor: '#e2e8f0',
    margin: '24px 0',
  },
  link: {
    color: '#3182ce',
    textDecoration: 'none',
  },
  button: {
    backgroundColor: '#4f46e5',
    borderRadius: '4px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '600',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
  },
  footer: {
    textAlign: 'center' as const,
    padding: '20px',
  },
  footerText: {
    fontSize: '12px',
    color: '#a0aec0',
    margin: '4px 0',
  },
};
