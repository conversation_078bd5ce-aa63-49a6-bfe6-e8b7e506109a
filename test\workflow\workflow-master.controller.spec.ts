/**
 * Workflow Master Controller Unit Tests
 *
 * Comprehensive test suite for WorkflowMasterController covering all REST endpoints,
 * authentication, validation, and error handling.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { WorkflowMasterController } from '../../src/workflow/workflow-master.controller';
import { WorkflowMasterService } from '../../src/workflow/workflow-master.service';
import {
  CreateWorkflowMasterDto,
  UpdateWorkflowMasterDto,
  WorkflowMasterFiltersDto,
} from '../../src/workflow/dto/workflow-master.dto';
import { IJWTPayload } from '../../src/types/auth';

describe('WorkflowMasterController', () => {
  let controller: WorkflowMasterController;
  let service: WorkflowMasterService;

  // Mock data
  const mockUser: IJWTPayload = {
    id: 'admin123',
    email: '<EMAIL>',
    sub: { name: 'Admin User' },
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockWorkflowMaster = {
    id: 'clx1234567890abcdef',
    name: 'Standard Immigration Workflow',
    description: 'Complete workflow template for immigration applications',
    is_active: true,
    created_by: 'Admin User', // Now stores admin name instead of ID
    updated_by: null,
    created_at: new Date('2025-01-06T10:30:00.000Z'),
    updated_at: new Date('2025-01-06T10:30:00.000Z'),
  };

  const mockCreateDto: CreateWorkflowMasterDto = {
    name: 'Standard Immigration Workflow',
    description: 'Complete workflow template for immigration applications',
    is_active: true,
  };

  const mockUpdateDto: UpdateWorkflowMasterDto = {
    name: 'Updated Immigration Workflow',
    description: 'Updated description',
  };

  const mockFilters: WorkflowMasterFiltersDto = {
    page: 1,
    limit: 10,
    search: 'immigration',
  };

  const mockPaginatedResponse = {
    data: [mockWorkflowMaster],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
  };

  // Mock WorkflowMasterService
  const mockWorkflowMasterService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    findActive: jest.fn(),
    toggleActive: jest.fn(),
    checkUsage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkflowMasterController],
      providers: [
        {
          provide: WorkflowMasterService,
          useValue: mockWorkflowMasterService,
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkflowMasterController>(WorkflowMasterController);
    service = module.get<WorkflowMasterService>(WorkflowMasterService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a workflow master successfully', async () => {
      mockWorkflowMasterService.create.mockResolvedValue(mockWorkflowMaster);

      const result = await controller.create(mockCreateDto, mockUser);

      expect(mockWorkflowMasterService.create).toHaveBeenCalledWith(
        mockCreateDto,
        mockUser,
      );
      expect(result).toEqual(mockWorkflowMaster);
    });

    it('should handle ConflictException during creation', async () => {
      mockWorkflowMasterService.create.mockRejectedValue(
        new ConflictException('Workflow master with this name already exists'),
      );

      await expect(controller.create(mockCreateDto, mockUser)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should handle service errors during creation', async () => {
      mockWorkflowMasterService.create.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.create(mockCreateDto, mockUser)).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated workflow masters', async () => {
      mockWorkflowMasterService.findAll.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.findAll(mockFilters);

      expect(mockWorkflowMasterService.findAll).toHaveBeenCalledWith(
        mockFilters,
      );
      expect(result).toEqual(mockPaginatedResponse);
    });

    it('should handle empty filters', async () => {
      const emptyFilters = {};
      mockWorkflowMasterService.findAll.mockResolvedValue({
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      });

      const result = await controller.findAll(
        emptyFilters as WorkflowMasterFiltersDto,
      );

      expect(mockWorkflowMasterService.findAll).toHaveBeenCalledWith(
        emptyFilters,
      );
      expect(result.data).toEqual([]);
    });

    it('should handle service errors during findAll', async () => {
      mockWorkflowMasterService.findAll.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.findAll(mockFilters)).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('findActive', () => {
    it('should return active workflow masters', async () => {
      const activeWorkflowMasters = [mockWorkflowMaster];
      mockWorkflowMasterService.findActive.mockResolvedValue(
        activeWorkflowMasters,
      );

      const result = await controller.findActive();

      expect(mockWorkflowMasterService.findActive).toHaveBeenCalled();
      expect(result).toEqual(activeWorkflowMasters);
    });

    it('should handle empty active workflow masters', async () => {
      mockWorkflowMasterService.findActive.mockResolvedValue([]);

      const result = await controller.findActive();

      expect(result).toEqual([]);
    });
  });

  describe('findOne', () => {
    it('should return a workflow master by ID', async () => {
      mockWorkflowMasterService.findOne.mockResolvedValue(mockWorkflowMaster);

      const result = await controller.findOne('clx1234567890abcdef');

      expect(mockWorkflowMasterService.findOne).toHaveBeenCalledWith(
        'clx1234567890abcdef',
      );
      expect(result).toEqual(mockWorkflowMaster);
    });

    it('should handle NotFoundException', async () => {
      mockWorkflowMasterService.findOne.mockRejectedValue(
        new NotFoundException('Workflow master not found'),
      );

      await expect(controller.findOne('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a workflow master successfully', async () => {
      const updatedWorkflowMaster = { ...mockWorkflowMaster, ...mockUpdateDto };
      mockWorkflowMasterService.update.mockResolvedValue(updatedWorkflowMaster);

      const result = await controller.update(
        'clx1234567890abcdef',
        mockUpdateDto,
        mockUser,
      );

      expect(mockWorkflowMasterService.update).toHaveBeenCalledWith(
        'clx1234567890abcdef',
        mockUpdateDto,
        mockUser,
      );
      expect(result).toEqual(updatedWorkflowMaster);
    });

    it('should handle NotFoundException during update', async () => {
      mockWorkflowMasterService.update.mockRejectedValue(
        new NotFoundException('Workflow master not found'),
      );

      await expect(
        controller.update('nonexistent', mockUpdateDto, mockUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle ConflictException during update', async () => {
      mockWorkflowMasterService.update.mockRejectedValue(
        new ConflictException('Workflow master with this name already exists'),
      );

      await expect(
        controller.update('clx1234567890abcdef', mockUpdateDto, mockUser),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('toggleActive', () => {
    it('should toggle workflow master active status', async () => {
      const toggledWorkflowMaster = { ...mockWorkflowMaster, is_active: false };
      mockWorkflowMasterService.toggleActive.mockResolvedValue(
        toggledWorkflowMaster,
      );

      const result = await controller.toggleActive(
        'clx1234567890abcdef',
        mockUser,
      );

      expect(mockWorkflowMasterService.toggleActive).toHaveBeenCalledWith(
        'clx1234567890abcdef',
        mockUser,
      );
      expect(result).toEqual(toggledWorkflowMaster);
    });

    it('should handle NotFoundException during toggle', async () => {
      mockWorkflowMasterService.toggleActive.mockRejectedValue(
        new NotFoundException('Workflow master not found'),
      );

      await expect(
        controller.toggleActive('nonexistent', mockUser),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should delete a workflow master successfully', async () => {
      mockWorkflowMasterService.remove.mockResolvedValue(undefined);

      const result = await controller.remove('clx1234567890abcdef');

      expect(mockWorkflowMasterService.remove).toHaveBeenCalledWith(
        'clx1234567890abcdef',
      );
      expect(result).toEqual({
        message: 'Workflow master deleted successfully',
      });
    });

    it('should handle NotFoundException during deletion', async () => {
      mockWorkflowMasterService.remove.mockRejectedValue(
        new NotFoundException('Workflow master not found'),
      );

      await expect(controller.remove('nonexistent')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle ConflictException during deletion', async () => {
      mockWorkflowMasterService.remove.mockRejectedValue(
        new ConflictException('Cannot delete workflow master as it is in use'),
      );

      await expect(controller.remove('clx1234567890abcdef')).rejects.toThrow(
        ConflictException,
      );
    });
  });
});
