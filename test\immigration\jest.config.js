module.exports = {
  displayName: 'Immigration Module Tests',
  testMatch: ['<rootDir>/test/immigration/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/immigration/**/*.(t|j)s',
    '!src/immigration/**/*.spec.ts',
    '!src/immigration/**/*.interface.ts',
  ],
  coverageDirectory: 'coverage/immigration',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/config/setup.ts'],
  testTimeout: 30000,
  verbose: true,
};
