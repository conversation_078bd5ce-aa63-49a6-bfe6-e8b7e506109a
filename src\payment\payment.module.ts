/**
 * Payment Module
 *
 * This module encapsulates all payment-related functionality for the Career Ireland platform.
 * It provides a complete payment processing system with Stripe integration, supporting
 * multiple service types and both authenticated and guest user flows.
 *
 * Module Components:
 * - PaymentController: Legacy REST API endpoints (v1)
 * - UnifiedPaymentController: New unified REST API endpoints (v2)
 * - PaymentService: Legacy payment processing logic (maintained for backward compatibility)
 * - UnifiedPaymentService: New unified payment processing logic
 * - StripeProvider: Stripe client configuration and initialization
 * - PrismaService: Database operations for payment records
 * - JwtService: JWT token handling for authenticated users
 * - MailerService: Email notifications for payment confirmations
 *
 * Features Provided:
 * - Stripe Checkout Session creation (legacy and unified)
 * - Webhook handling for payment confirmations
 * - Email notifications (customer and admin)
 * - Payment history tracking and analytics
 * - Support for multiple service types (mentor, package, immigration, training)
 * - Guest and authenticated user payment flows
 * - Backward compatibility during migration
 *
 * Migration Strategy:
 * - Both legacy (v1) and unified (v2) APIs are available
 * - Gradual migration from legacy to unified endpoints
 * - Feature flags for controlled rollout
 * - Data consistency between old and new tables
 *
 * Dependencies:
 * - Stripe: Payment processing
 * - Prisma: Database ORM
 * - NestJS JWT: Authentication
 * - Custom Mailer: Email notifications
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import { Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { UnifiedPaymentController } from './unified-payment.controller';
import { UnifiedPaymentService } from './unified-payment.service';
import { StripeProvider } from 'src/config/stripe.config';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';
import { LoggerService } from 'src/utils/logger.service';
import { ApplicationModule } from '../application/application.module';

@Module({
  imports: [
    ApplicationModule, // Task 6: Import application module for payment integration
  ],
  controllers: [
    PaymentController, // Legacy v1 endpoints (/payment/*)
    UnifiedPaymentController, // New v2 endpoints (/v2/payment/*)
  ],
  providers: [
    PaymentService, // Legacy payment service (backward compatibility)
    UnifiedPaymentService, // New unified payment service
    StripeProvider,
    PrismaService,
    JwtService,
    MailerService,
    LoggerService,
  ],
  exports: [
    PaymentService, // Export for other modules that depend on legacy service
    UnifiedPaymentService, // Export for other modules that want to use unified service
  ],
})
export class PaymentModule {}
