model review {
  id        String   @id @default(cuid())
  message   String
  mentorId  String?
  userId    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  rating    Int
  mentor    mentor?  @relation(fields: [mentorId], references: [id])
  user      user?    @relation(fields: [userId], references: [id])
}

model customer_review {
  id        String   @id @default(cuid())
  name      String
  img       String?
  comment   String
  source    String
  rating    Int
  date      DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Int?
}
