import {
  Controller,
  DefaultValuePipe,
  Get,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { GuestService } from './guest.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('guest')
@Controller('guest')
export class GuestController {
  constructor(private guest: GuestService) {}

  @UseGuards(JwtAdmin)
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  @ApiBearerAuth()
  @Get('purchase/service')
  async getServices(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.guest.service(page, limit);
  }
  @UseGuards(JwtAdmin)
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  @ApiBearerAuth()
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  @Get('purchase/package')
  async getPackage(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.guest.package(page, limit);
  }
  @UseGuards(JwtAdmin)
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  @ApiBearerAuth()
  @Get('purchase/immigration')
  async getImmigration(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.guest.immigration(page, limit);
  }

  @UseGuards(JwtAdmin)
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  @ApiBearerAuth()
  @Get('purchase/training')
  async getTraining(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.guest.training(page, limit);
  }
}
