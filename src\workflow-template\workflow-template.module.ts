/**
 * Workflow Template Module
 *
 * This module provides workflow template CRUD functionality for the Career Ireland platform.
 * It includes controller, service, and all necessary dependencies for workflow template management.
 *
 * Key Features:
 * - Admin-only workflow template management
 * - Full CRUD operations with validation
 * - Service-agnostic design (immigration, training, packages, consulting)
 * - Usage checking before deletion
 * - Comprehensive error handling and logging
 * - Integration with existing authentication system
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { WorkflowTemplateService } from './workflow-template.service';
import { WorkflowTemplateController } from './workflow-template.controller';
import { PrismaService } from '../utils/prisma.service';

@Module({
  imports: [
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [WorkflowTemplateController],
  providers: [
    WorkflowTemplateService,
    PrismaService,
  ],
  exports: [WorkflowTemplateService],
})
export class WorkflowTemplateModule {}
