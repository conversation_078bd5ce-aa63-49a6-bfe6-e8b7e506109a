-- Update DocumentStatus enum to add new values
-- This migration maintains backward compatibility

-- Add the new enum values (PostgreSQL requires separate transactions for enum additions)
-- We'll add them one by one to avoid transaction issues

-- Add Required_Revision value
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'Required_Revision' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'DocumentStatus')) THEN
        ALTER TYPE "DocumentStatus" ADD VALUE 'Required_Revision';
    END IF;
END $$;

-- Add Request value
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'Request' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'DocumentStatus')) THEN
        ALTER TYPE "DocumentStatus" ADD VALUE 'Request';
    END IF;
END $$;

-- Note: Data updates are commented out to avoid enum transaction issues
-- These updates should be run separately after the enum values are committed

-- Update existing records that use the old enum values
-- Map Requires_Revision to Required_Revision in document_vault table
-- UPDATE "document_vault"
-- SET "status" = 'Required_Revision'
-- WHERE "status" = 'Requires_Revision';

-- Update application_document table (uses lowercase string values)
-- UPDATE "application_document"
-- SET "status" = 'required_revision'
-- WHERE "status" = 'requires_revision';

-- Map deprecated statuses to appropriate alternatives
-- Superseded documents -> Expired (as they are no longer current)
-- UPDATE "document_vault"
-- SET "status" = 'Expired'
-- WHERE "status" = 'Superseded';

-- Archived documents -> Approved (as they were likely approved before archiving)
-- UPDATE "document_vault"
-- SET "status" = 'Approved'
-- WHERE "status" = 'Archived';

-- Add comment to track this migration
COMMENT ON TYPE "DocumentStatus" IS 'Updated 2025-06-16: Added Required_Revision and Request values. Deprecated Superseded and Archived values mapped to alternatives.';
