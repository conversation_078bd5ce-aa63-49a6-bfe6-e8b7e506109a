/**
 * Workflow Template Integration Tests
 *
 * This file contains comprehensive integration tests for the workflow template module.
 * Tests cover end-to-end API functionality, database operations, and authentication.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { WorkflowTemplateModule } from '../../src/workflow-template/workflow-template.module';
import { PrismaService } from '../../src/utils/prisma.service';

describe('WorkflowTemplate Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let adminToken: string;

  // Test data
  const testWorkflowTemplate = {
    name: 'Test Immigration Workflow Template',
    description: 'Test workflow template for integration testing',
    serviceType: 'immigration',
    serviceId: 'test_service_123',
    isActive: true,
    workflowTemplate: [
      {
        stageName: 'Document Collection',
        stageOrder: 1,
        description: 'Collect all required documents',
        estimatedDuration: 5,
        documentsRequired: true,
        documents: [
          { documentName: 'passport', required: true },
          { documentName: 'visa', required: true },
          { documentName: 'photo', required: false },
        ],
        customFormRequired: true,
        customForm: [
          { fieldName: 'fullName', fieldType: 'text', required: true },
          { fieldName: 'age', fieldType: 'number', required: true },
          { fieldName: 'address', fieldType: 'textarea', required: true },
        ],
        assigneeRole: 'applicant',
        autoAdvance: false,
      },
      {
        stageName: 'Initial Review',
        stageOrder: 2,
        description: 'Admin reviews submitted documents',
        estimatedDuration: 3,
        documentsRequired: false,
        customFormRequired: false,
        assigneeRole: 'admin',
        autoAdvance: true,
      },
      {
        stageName: 'Final Approval',
        stageOrder: 3,
        description: 'Final approval and processing',
        estimatedDuration: 2,
        documentsRequired: false,
        customFormRequired: false,
        assigneeRole: 'admin',
        autoAdvance: false,
      },
    ],
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [WorkflowTemplateModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    
    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate admin JWT token for testing
    adminToken = jwtService.sign(
      {
        id: 'test-admin-id',
        email: '<EMAIL>',
        sub: { name: 'Test Admin' },
      },
      { secret: process.env.jwtAdminSecretKey || 'test-admin-secret' },
    );

    await app.init();
  });

  afterAll(async () => {
    // Clean up test data
    await prismaService.workflow_template.deleteMany({
      where: {
        name: {
          contains: 'Test',
        },
      },
    });

    await app.close();
  });

  describe('POST /workflow-templates', () => {
    it('should create a workflow template successfully', async () => {
      const response = await request(app.getHttpServer())
        .post('/workflow-templates')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(testWorkflowTemplate)
        .expect(201);

      expect(response.body).toMatchObject({
        name: testWorkflowTemplate.name,
        description: testWorkflowTemplate.description,
        serviceType: testWorkflowTemplate.serviceType,
        serviceId: testWorkflowTemplate.serviceId,
        isActive: testWorkflowTemplate.isActive,
        workflowTemplate: testWorkflowTemplate.workflowTemplate,
        createdBy: 'Test Admin',
      });

      expect(response.body.id).toBeDefined();
      expect(response.body.createdAt).toBeDefined();
      expect(response.body.updatedAt).toBeDefined();

      // Store the created template ID for cleanup
      testWorkflowTemplate['id'] = response.body.id;
    });

    it('should return 400 for invalid workflow template structure', async () => {
      const invalidTemplate = {
        ...testWorkflowTemplate,
        name: 'Invalid Template',
        workflowTemplate: [
          {
            stageName: 'Stage 1',
            stageOrder: 1,
          },
          {
            stageName: 'Stage 2',
            stageOrder: 1, // Duplicate order
          },
        ],
      };

      await request(app.getHttpServer())
        .post('/workflow-templates')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidTemplate)
        .expect(400);
    });

    it('should return 409 for duplicate template name', async () => {
      const duplicateTemplate = {
        ...testWorkflowTemplate,
        serviceId: 'different_service',
      };

      await request(app.getHttpServer())
        .post('/workflow-templates')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicateTemplate)
        .expect(409);
    });

    it('should return 401 without admin token', async () => {
      await request(app.getHttpServer())
        .post('/workflow-templates')
        .send(testWorkflowTemplate)
        .expect(401);
    });
  });

  describe('GET /workflow-templates', () => {
    it('should return paginated workflow templates', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({
          page: 1,
          limit: 10,
          sortBy: 'createdAt',
          sortOrder: 'desc',
        })
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('limit', 10);
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should filter by service type', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({
          serviceType: 'immigration',
          page: 1,
          limit: 10,
        })
        .expect(200);

      expect(response.body.data.every((template: any) => 
        template.serviceType === 'immigration'
      )).toBe(true);
    });

    it('should search by name', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({
          search: 'Test Immigration',
          page: 1,
          limit: 10,
        })
        .expect(200);

      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].name).toContain('Test Immigration');
    });
  });

  describe('GET /workflow-templates/service-type/:serviceType', () => {
    it('should return templates by service type', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates/service-type/immigration')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.every((template: any) => 
        template.serviceType === 'immigration' && template.isActive === true
      )).toBe(true);
    });
  });

  describe('GET /workflow-templates/:id', () => {
    it('should return a specific workflow template', async () => {
      const templateId = testWorkflowTemplate['id'];

      const response = await request(app.getHttpServer())
        .get(`/workflow-templates/${templateId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: templateId,
        name: testWorkflowTemplate.name,
        serviceType: testWorkflowTemplate.serviceType,
      });
    });

    it('should return 404 for non-existent template', async () => {
      await request(app.getHttpServer())
        .get('/workflow-templates/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /workflow-templates/:id/usage', () => {
    it('should return usage information', async () => {
      const templateId = testWorkflowTemplate['id'];

      const response = await request(app.getHttpServer())
        .get(`/workflow-templates/${templateId}/usage`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('templateId', templateId);
      expect(response.body).toHaveProperty('activeApplications');
      expect(response.body).toHaveProperty('totalApplications');
      expect(response.body).toHaveProperty('canDelete');
      expect(response.body).toHaveProperty('usageDetails');
      expect(Array.isArray(response.body.usageDetails)).toBe(true);
    });
  });

  describe('PATCH /workflow-templates/:id', () => {
    it('should update a workflow template successfully', async () => {
      const templateId = testWorkflowTemplate['id'];
      const updateData = {
        name: 'Updated Test Immigration Workflow Template',
        description: 'Updated description for testing',
      };

      const response = await request(app.getHttpServer())
        .patch(`/workflow-templates/${templateId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        id: templateId,
        name: updateData.name,
        description: updateData.description,
        updatedBy: 'Test Admin',
      });
    });

    it('should return 404 for non-existent template', async () => {
      await request(app.getHttpServer())
        .patch('/workflow-templates/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ name: 'Updated Name' })
        .expect(404);
    });
  });



  describe('DELETE /workflow-templates/:id', () => {
    it('should delete a workflow template successfully', async () => {
      const templateId = testWorkflowTemplate['id'];

      await request(app.getHttpServer())
        .delete(`/workflow-templates/${templateId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(204);

      // Verify template is deleted
      await request(app.getHttpServer())
        .get(`/workflow-templates/${templateId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should return 404 for non-existent template', async () => {
      await request(app.getHttpServer())
        .delete('/workflow-templates/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /workflow-templates/service/:serviceId', () => {
    let immigrationServiceId: string;
    let workflowTemplateId: string;

    beforeEach(async () => {
      // Create a test immigration service
      const immigrationService = await prismaService.immigration_service.create({
        data: {
          name: 'Test Immigration Service',
          amount: 1000,
          service: ['visa_application', 'document_review'],
          order: 1,
          website_visible: true,
        },
      });
      immigrationServiceId = immigrationService.id;

      // Create a workflow template for this immigration service
      const workflowTemplate = await prismaService.workflow_template.create({
        data: {
          name: 'Test Immigration Workflow for Service',
          description: 'Test workflow template for specific immigration service',
          serviceType: 'immigration',
          serviceId: immigrationServiceId,
          isActive: true,
          workflowTemplate: [
            {
              stageName: 'Document Collection',
              stageOrder: 1,
              description: 'Collect all required documents',
              estimatedDuration: 5,
              documentsRequired: true,
              documents: [
                { documentName: 'passport', required: true },
                { documentName: 'visa', required: true },
              ],
              customFormRequired: true,
              customForm: [
                { fieldName: 'fullName', fieldType: 'text', required: true },
                { fieldName: 'age', fieldType: 'number', required: true },
              ],
              assigneeRole: 'applicant',
            },
          ],
          createdBy: 'Test Admin',
        },
      });
      workflowTemplateId = workflowTemplate.id;
    });

    afterEach(async () => {
      // Clean up test data
      if (workflowTemplateId) {
        await prismaService.workflow_template.delete({
          where: { id: workflowTemplateId },
        });
      }
      if (immigrationServiceId) {
        await prismaService.immigration_service.delete({
          where: { id: immigrationServiceId },
        });
      }
    });

    it('should return workflow templates for valid service ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workflow-templates/service/${immigrationServiceId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toBeInstanceOf(Array);
      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toMatchObject({
        id: workflowTemplateId,
        name: 'Test Immigration Workflow for Service',
        serviceType: 'immigration',
        serviceId: immigrationServiceId,
        isActive: true,
      });
    });

    it('should return empty array when no templates exist for service', async () => {
      // Create another immigration service without templates
      const anotherService = await prismaService.immigration_service.create({
        data: {
          name: 'Another Immigration Service',
          amount: 1500,
          service: ['work_permit'],
          order: 2,
          website_visible: true,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/workflow-templates/service/${anotherService.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toBeInstanceOf(Array);
      expect(response.body).toHaveLength(0);

      // Clean up
      await prismaService.immigration_service.delete({
        where: { id: anotherService.id },
      });
    });

    it('should return empty array for non-existent service ID', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates/service/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toBeInstanceOf(Array);
      expect(response.body).toHaveLength(0);
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .get(`/workflow-templates/service/${immigrationServiceId}`)
        .expect(401);
    });
  });

  describe('GET /workflow-templates with serviceId filter', () => {
    let immigrationServiceId: string;
    let workflowTemplateId: string;

    beforeEach(async () => {
      // Create a test immigration service
      const immigrationService = await prismaService.immigration_service.create({
        data: {
          name: 'Test Immigration Service for Filter',
          amount: 2000,
          service: ['student_visa', 'work_permit'],
          order: 1,
          website_visible: true,
        },
      });
      immigrationServiceId = immigrationService.id;

      // Create a workflow template for this immigration service
      const workflowTemplate = await prismaService.workflow_template.create({
        data: {
          name: 'Test Immigration Workflow for Filter',
          description: 'Test workflow template for service filter',
          serviceType: 'immigration',
          serviceId: immigrationServiceId,
          isActive: true,
          workflowTemplate: [
            {
              stageName: 'Initial Review',
              stageOrder: 1,
              description: 'Initial review of application',
              estimatedDuration: 3,
              documentsRequired: false,
              customFormRequired: false,
              assigneeRole: 'agent',
            },
          ],
          createdBy: 'Test Admin',
        },
      });
      workflowTemplateId = workflowTemplate.id;
    });

    afterEach(async () => {
      // Clean up test data
      if (workflowTemplateId) {
        await prismaService.workflow_template.delete({
          where: { id: workflowTemplateId },
        });
      }
      if (immigrationServiceId) {
        await prismaService.immigration_service.delete({
          where: { id: immigrationServiceId },
        });
      }
    });

    it('should filter workflow templates by service ID', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .query({ serviceId: immigrationServiceId })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body).toHaveProperty('totalPages');

      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toMatchObject({
        id: workflowTemplateId,
        name: 'Test Immigration Workflow for Filter',
        serviceType: 'immigration',
        serviceId: immigrationServiceId,
        isActive: true,
      });
    });

    it('should filter by serviceId with non-immigration serviceType', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .query({
          serviceType: 'training',
          serviceId: 'training_service_123',
        })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      // Should return empty array since no training templates exist
      expect(response.body.data).toHaveLength(0);
    });

    it('should return 404 for non-existent immigration service ID in filter', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .query({ serviceId: 'non-existent-id' })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.message).toContain('Immigration service with ID non-existent-id not found');
    });

    it('should allow serviceId with immigration serviceType', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-templates')
        .query({
          serviceType: 'immigration',
          serviceId: immigrationServiceId,
        })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].serviceId).toBe(immigrationServiceId);
    });
  });
});
