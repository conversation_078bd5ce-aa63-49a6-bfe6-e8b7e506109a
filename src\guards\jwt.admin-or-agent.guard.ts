import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

/**
 * Guard that allows access for both admin and agent users
 * Useful for endpoints that should be accessible by both user types
 */
@Injectable()
export class JwtAdminOrAgent implements CanActivate {
  constructor(private jwtService: JwtService) {}
  
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) throw new UnauthorizedException('There is no bearer token');

    try {
      // Try to verify with admin secret first
      let payload;
      let tokenType;
      
      try {
        payload = await this.jwtService.verifyAsync(token, {
          secret: process.env.jwtAdminSecretKey,
        });
        tokenType = 'admin';
      } catch {
        // If admin verification fails, try agent secret
        try {
          payload = await this.jwtService.verifyAsync(token, {
            secret: process.env.jwtAgentSecretKey,
          });
          tokenType = 'agent';
          
          // Ensure the token is specifically for agents
          if (payload.tokenType !== 'agent') {
            throw new UnauthorizedException('Invalid token type');
          }
        } catch {
          throw new UnauthorizedException('Invalid token for admin or agent access');
        }
      }
      
      // Add token type to payload for downstream use
      payload.tokenType = tokenType;
      request['user'] = payload;
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    return true;
  }

  private extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
