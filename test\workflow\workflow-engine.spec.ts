/**
 * Workflow Engine Service Tests
 * Comprehensive unit tests for WorkflowEngineService
 */

import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowEngineService } from '../../src/application/services/workflow-engine.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { WorkflowStepStatus, PriorityLevel } from '@prisma/client';

describe('WorkflowEngineService', () => {
  let service: WorkflowEngineService;
  let prismaService: PrismaService;

  const mockApplication = {
    id: 'app-123',
    application_number: 'IMM-2024-000001',
    application_type: 'immigration',
    service_type: 'immigration',
    status: 'Draft',
    priority_level: PriorityLevel.Medium,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockWorkflowTemplate = {
    id: 'template-123',
    name: 'Immigration Application Workflow',
    application_type: 'immigration',
    is_active: true,
    version: '1.0',
    steps_configuration: [
      {
        step_name: 'Document Collection',
        step_order: 1,
        estimated_duration: 24,
        assignee_role: 'applicant',
      },
      {
        step_name: 'Initial Review',
        step_order: 2,
        estimated_duration: 48,
        assignee_role: 'admin',
      },
    ],
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockApplicationSteps = [
    {
      id: 'step-1',
      application_id: 'app-123',
      step_name: 'Document Collection',
      step_order: 1,
      status: WorkflowStepStatus.Not_Started,
      estimated_duration: 24,
      sla_threshold: 48,
      assignee_role: 'applicant',
      required_fields: null,
      validation_rules: null,
      completion_criteria: null,
      started_at: null,
      completed_at: null,
      due_date: null,
      assigned_to: null,
      reviewer_id: null,
      review_notes: null,
      step_data: null,
      attachments: null,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: 'step-2',
      application_id: 'app-123',
      step_name: 'Initial Review',
      step_order: 2,
      status: WorkflowStepStatus.Not_Started,
      estimated_duration: 48,
      sla_threshold: 72,
      assignee_role: 'admin',
      required_fields: null,
      validation_rules: null,
      completion_criteria: null,
      started_at: null,
      completed_at: null,
      due_date: null,
      assigned_to: null,
      reviewer_id: null,
      review_notes: null,
      step_data: null,
      attachments: null,
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowEngineService,
        {
          provide: PrismaService,
          useValue: {
            application_step: {
              createMany: jest.fn(),
              findMany: jest.fn(),
              findFirst: jest.fn(),
              update: jest.fn(),
            },
            application: {
              update: jest.fn(),
            },
            workflow_template: {
              findFirst: jest.fn(),
            },
            application_document: {
              findMany: jest.fn(),
            },
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<WorkflowEngineService>(WorkflowEngineService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initializeWorkflow', () => {
    it('should initialize workflow steps from template', async () => {
      jest
        .spyOn(prismaService.application_step, 'createMany')
        .mockResolvedValue({ count: 2 });
      jest
        .spyOn(prismaService.application_step, 'findMany')
        .mockResolvedValue(mockApplicationSteps);
      jest
        .spyOn(prismaService.application, 'update')
        .mockResolvedValue(mockApplication as any);

      const result = await service.initializeWorkflow(
        mockApplication as any,
        mockWorkflowTemplate as any,
      );

      expect(prismaService.application_step.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            application_id: 'app-123',
            step_name: 'Document Collection',
            step_order: 1,
            status: WorkflowStepStatus.Not_Started,
          }),
          expect.objectContaining({
            application_id: 'app-123',
            step_name: 'Initial Review',
            step_order: 2,
            status: WorkflowStepStatus.Not_Started,
          }),
        ]),
      });

      expect(result).toEqual(mockApplicationSteps);
    });

    it('should handle invalid steps configuration', async () => {
      const invalidTemplate = {
        ...mockWorkflowTemplate,
        steps_configuration: 'invalid',
      };

      await expect(
        service.initializeWorkflow(
          mockApplication as any,
          invalidTemplate as any,
        ),
      ).rejects.toThrow('Invalid steps configuration in workflow template');
    });
  });

  describe('advanceWorkflow', () => {
    it('should advance workflow to next step', async () => {
      const currentStep = mockApplicationSteps[0];
      const nextStep = mockApplicationSteps[1];

      jest
        .spyOn(prismaService.application_step, 'findFirst')
        .mockResolvedValueOnce(currentStep as any)
        .mockResolvedValueOnce(nextStep as any);

      jest.spyOn(prismaService.application_step, 'update').mockResolvedValue({
        ...currentStep,
        status: WorkflowStepStatus.Completed,
        completed_at: new Date(),
      } as any);

      jest
        .spyOn(prismaService.application, 'update')
        .mockResolvedValue(mockApplication as any);
      jest.spyOn(service, 'validateStepCompletion').mockResolvedValue(true);

      const result = await service.advanceWorkflow('app-123', 1, {
        data: 'test',
      });

      expect(prismaService.application_step.update).toHaveBeenCalledWith({
        where: { id: 'step-1' },
        data: expect.objectContaining({
          status: WorkflowStepStatus.Completed,
          step_data: { data: 'test' },
          completed_at: expect.any(Date),
        }),
      });

      expect(result.status).toBe(WorkflowStepStatus.Completed);
    });

    it('should throw error if step not found', async () => {
      jest
        .spyOn(prismaService.application_step, 'findFirst')
        .mockResolvedValue(null);

      await expect(service.advanceWorkflow('app-123', 1, {})).rejects.toThrow(
        'Step 1 not found for application app-123',
      );
    });

    it('should throw error if step completion criteria not met', async () => {
      jest
        .spyOn(prismaService.application_step, 'findFirst')
        .mockResolvedValue(mockApplicationSteps[0] as any);
      jest.spyOn(service, 'validateStepCompletion').mockResolvedValue(false);

      await expect(service.advanceWorkflow('app-123', 1, {})).rejects.toThrow(
        'Step 1 completion criteria not met',
      );
    });
  });

  describe('validateStepCompletion', () => {
    it('should return true when no criteria specified', async () => {
      const step = { ...mockApplicationSteps[0], completion_criteria: null };
      const result = await service.validateStepCompletion(step as any, {});
      expect(result).toBe(true);
    });

    it('should validate required fields', async () => {
      const step = {
        ...mockApplicationSteps[0],
        completion_criteria: {
          required_fields: ['name', 'email'],
        },
      };

      // Test with missing fields
      let result = await service.validateStepCompletion(step as any, {
        name: 'John',
      });
      expect(result).toBe(false);

      // Test with all required fields
      result = await service.validateStepCompletion(step as any, {
        name: 'John',
        email: '<EMAIL>',
      });
      expect(result).toBe(true);
    });
  });

  describe('checkStepDependencies', () => {
    it('should return true when no dependencies', async () => {
      const step = mockApplicationSteps[0];
      const result = await service.checkStepDependencies(
        step as any,
        mockApplicationSteps as any,
      );
      expect(result).toBe(true);
    });

    it('should validate step dependencies', async () => {
      const stepWithDependencies = {
        ...mockApplicationSteps[1],
        validation_rules: {
          dependencies: [1],
        },
      };

      // Test with incomplete dependency
      let result = await service.checkStepDependencies(
        stepWithDependencies as any,
        mockApplicationSteps as any,
      );
      expect(result).toBe(false);

      // Test with completed dependency
      const completedSteps = [
        { ...mockApplicationSteps[0], status: WorkflowStepStatus.Completed },
        mockApplicationSteps[1],
      ];
      result = await service.checkStepDependencies(
        stepWithDependencies as any,
        completedSteps as any,
      );
      expect(result).toBe(true);
    });
  });

  describe('checkOverdueSteps', () => {
    it('should return overdue steps', async () => {
      const overdueSteps = [
        {
          ...mockApplicationSteps[0],
          due_date: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          application: mockApplication,
        },
      ];

      jest
        .spyOn(prismaService.application_step, 'findMany')
        .mockResolvedValue(overdueSteps as any);

      const result = await service.checkOverdueSteps();

      expect(prismaService.application_step.findMany).toHaveBeenCalledWith({
        where: {
          status: {
            in: [
              WorkflowStepStatus.In_Progress,
              WorkflowStepStatus.Not_Started,
            ],
          },
          due_date: {
            lt: expect.any(Date),
          },
        },
        include: {
          application: true,
        },
      });

      expect(result).toEqual(overdueSteps);
    });
  });

  describe('getActiveTemplate', () => {
    it('should return active workflow template', async () => {
      jest
        .spyOn(prismaService.workflow_template, 'findFirst')
        .mockResolvedValue(mockWorkflowTemplate as any);

      const result = await service.getActiveTemplate(
        'immigration',
        'immigration',
      );

      expect(prismaService.workflow_template.findFirst).toHaveBeenCalledWith({
        where: {
          application_type: 'immigration',
          service_type: 'immigration',
          is_active: true,
        },
        orderBy: { created_at: 'desc' },
      });

      expect(result).toEqual(mockWorkflowTemplate);
    });

    it('should return null when no template found', async () => {
      jest
        .spyOn(prismaService.workflow_template, 'findFirst')
        .mockResolvedValue(null);

      const result = await service.getActiveTemplate('nonexistent', 'type');

      expect(result).toBeNull();
    });
  });
});
