/**
 * Fallback Email Templates
 *
 * This module contains fallback email templates used when the main React email
 * templates fail to render. These templates provide a basic HTML structure
 * to ensure email notifications are still sent even when template rendering fails.
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

/**
 * Get fallback customer email template
 * Used when the main template rendering fails
 */
export function getFallbackCustomerTemplate(
  serviceName: string,
  customerName: string,
): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Confirmation</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f6f9fc;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
          }
          .content {
            padding: 20px 0;
          }
          .footer {
            padding: 20px 0;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 20px;
          }
          .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            background: white;
            border-radius: 8px;
            padding: 8px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🎯</div>
            <h1>Payment Successful</h1>
          </div>
          <div class="content">
            <p>Dear ${customerName || 'Customer'},</p>
            <p>Your payment for <strong>${serviceName}</strong> has been processed successfully.</p>
            <p>We're excited to help you on your career journey!</p>
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
          </div>
          <div class="footer">
            <p>Thank you for choosing Career Ireland!</p>
            <p><small>This is an automated message. Please do not reply to this email.</small></p>
          </div>
        </div>
      </body>
    </html>
  `;
}

/**
 * Get fallback admin email template
 * Used when the main template rendering fails
 */
export function getFallbackAdminTemplate(
  serviceName: string,
  customerName: string,
  customerEmail: string,
): string {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Payment Received</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f6f9fc;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .header {
            background: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
          }
          .content {
            padding: 20px 0;
          }
          .details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
          }
          .footer {
            padding: 20px 0;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 20px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>💰 New Payment Received</h1>
          </div>
          <div class="content">
            <p>A new payment has been processed successfully.</p>
            <div class="details">
              <p><strong>Customer:</strong> ${customerName || 'Unknown'}</p>
              <p><strong>Email:</strong> ${customerEmail || 'Unknown'}</p>
              <p><strong>Service:</strong> ${serviceName}</p>
              <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
            </div>
            <p>Please check the admin dashboard for complete payment details.</p>
          </div>
          <div class="footer">
            <p>Career Ireland Admin Notification System</p>
          </div>
        </div>
      </body>
    </html>
  `;
}