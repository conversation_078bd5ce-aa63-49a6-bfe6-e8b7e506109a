import { BadRequestException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';
import { PrismaService } from 'src/utils/prisma.service';
import { ForgotPasswordDto, ResetPasswordDto } from './dto/password.dto';
import { render } from '@react-email/components';
import ResetPasswordEmail from 'src/template/reset-password';
import { hash } from 'bcrypt';

@Injectable()
export class PasswordService {
  constructor(
    private prisma: PrismaService,
    private readonly jwt: JwtService,
    private readonly mailer: MailerService,
  ) {}

  async forgotPassword(dto: ForgotPasswordDto) {
    const user = await this.prisma.user.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (!user) {
      throw new BadRequestException('No user found with the given email.');
    }

    const resetToken = await this.jwt.signAsync(
      { email: user.email },
      {
        secret: process.env.RESET_PASSWORD_SECRET,
        expiresIn: '15m', // Token expires in 15 minutes
      },
    );
    const resetUrl = `${process.env.WEBSITE}/auth/reset-password?token=${resetToken}`;
    await this.mailer.sendEmail({
      to: user.email,
      subject: 'Reset Password',
      html: await render(
        ResetPasswordEmail({
          name: user.name,
          url: resetUrl,
        }),
      ),
      cc: [],
      from: process.env.EMAIL,
    });

    return {
      status: 'Ok',
      message: 'Password reset link has been sent to your email.',
    };
  }

  async resetPassword(dto: ResetPasswordDto) {
    try {
      const payload = await this.jwt.verifyAsync(dto.token, {
        secret: process.env.RESET_PASSWORD_SECRET,
      });

      const { email } = payload;

      const hashedPassword = await hash(dto.password, 10);
      await this.prisma.user.update({
        where: { email },
        data: { password: hashedPassword },
      });
      return {
        status: 'Ok',
        message: 'Password has been reset successfully.',
      };
    } catch (err) {
      throw new BadRequestException('Invalid or expired link.');
    }
  }
}
