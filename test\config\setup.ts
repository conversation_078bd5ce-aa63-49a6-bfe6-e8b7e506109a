/**
 * Jest Setup Configuration
 * Task 8: API Implementation and Documentation
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load test environment variables
config({ path: join(__dirname, '../../.env.test') });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/careerireland_test';

// Mock external services
jest.mock('../../src/utils/supabase.service', () => ({
  SupabaseService: jest.fn().mockImplementation(() => ({
    uploadFile: jest.fn().mockResolvedValue({
      status: 'OK',
      url: 'https://test-storage.com/test-file.pdf',
    }),
    deleteFile: jest.fn().mockResolvedValue({ status: 'OK' }),
    getFileUrl: jest.fn().mockReturnValue('https://test-storage.com/test-file.pdf'),
  })),
}));

jest.mock('../../src/mailer/mailer.service', () => ({
  MailerService: jest.fn().mockImplementation(() => ({
    sendEmail: jest.fn().mockResolvedValue({ success: true }),
  })),
}));

// Global test utilities
global.createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  ...overrides,
});

global.createMockAdmin = (overrides = {}) => ({
  id: 'test-admin-id',
  email: '<EMAIL>',
  name: 'Test Admin',
  role: 'admin',
  ...overrides,
});

global.createMockApplication = (overrides = {}) => ({
  id: 'test-app-id',
  application_number: 'TEST-2024-001',
  application_type: 'immigration',
  service_type: 'work_permit',
  status: 'In_Progress',
  priority_level: 'Medium',
  user_id: 'test-user-id',
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides,
});

global.createMockDocument = (overrides = {}) => ({
  id: 'test-doc-id',
  document_name: 'Test Document',
  original_filename: 'test.pdf',
  document_type: 'Passport',
  file_path: 'https://test-storage.com/test.pdf',
  file_size: 1024,
  mime_type: 'application/pdf',
  status: 'Pending',
  user_id: 'test-user-id',
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides,
});

global.createMockNotification = (overrides = {}) => ({
  id: 'test-notif-id',
  notification_type: 'Email',
  recipient_email: '<EMAIL>',
  subject: 'Test Notification',
  message_body: 'Test message',
  status: 'Sent',
  created_at: new Date(),
  sent_at: new Date(),
  ...overrides,
});

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test timeout
jest.setTimeout(30000);

// Unhandled promise rejection handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Type declarations for global test utilities
declare global {
  function createMockUser(overrides?: any): any;
  function createMockAdmin(overrides?: any): any;
  function createMockApplication(overrides?: any): any;
  function createMockDocument(overrides?: any): any;
  function createMockNotification(overrides?: any): any;
}
