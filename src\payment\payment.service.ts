/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * Payment Service
 *
 * This service handles all payment-related operations for the Career Ireland platform.
 * It integrates with <PERSON>e for payment processing and supports multiple service types:
 * - Mentor services (career consultation, CV review, etc.)
 * - Immigration services (visa consultation, document review, etc.)
 * - Training programs (skill development courses)
 * - Service packages (bundled offerings)
 *
 * The service supports both authenticated users and guest users, with separate
 * payment flows and database records for each type.
 *
 * Key Features:
 * - Stripe Checkout Session creation
 * - Webhook handling for payment confirmation
 * - Email notifications for successful payments
 * - Admin notifications for new purchases
 * - Payment history tracking
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import { Inject, Injectable } from '@nestjs/common';
import { STRIPE_CLIENT } from 'src/config/stripe.config';
import Stripe from 'stripe';
import {
  UserImmigrationDto,
  UserImmigrationServiceDto,
  UserMentorServiceDto,
  UserPackageDto,
  UserPackageServiceDto,
  UserServiceDto,
  UserTrainingDto,
  UserTrainingServiceDto,
} from './dto/payment.dto';
import { PrismaService } from 'src/utils/prisma.service';
import { MailerService } from 'src/mailer/mailer.service';
import { IJWTPayload } from 'src/types/auth';
import MentorPaymentSuccessEmail from 'src/template/service';
import { render } from '@react-email/components';
import PurchaseNotificationEmail from 'src/template/purchase-notification';

@Injectable()
export class PaymentService {
  /**
   * Payment Service Constructor
   *
   * Initializes the payment service with required dependencies:
   * - Stripe client for payment processing
   * - Prisma service for database operations
   * - Mailer service for email notifications
   *
   * @param stripe - Stripe client instance for payment processing
   * @param prisma - Prisma service for database operations
   * @param mailer - Mailer service for sending email notifications
   */
  constructor(
    @Inject(STRIPE_CLIENT) private stripe: Stripe,
    private prisma: PrismaService,
    private mailer: MailerService,
  ) {}

  /**
   * Process Mentor Service Payment for Authenticated Users
   *
   * Creates a Stripe checkout session for mentor service purchases by authenticated users.
   * This method handles the payment flow for career consultation, CV review, and other
   * mentor-provided services.
   *
   * Flow:
   * 1. Validates the service exists and is available
   * 2. Creates a Stripe checkout session with service details
   * 3. Returns the checkout URL for user redirection
   *
   * @param user - Authenticated user information from JWT token
   * @param dto - Payment request data containing serviceId
   * @returns Promise<{status: string, url: string}> - Checkout session URL
   * @throws Error if service not found or Stripe session creation fails
   */
  async mentor_service(user: IJWTPayload, dto: UserMentorServiceDto) {
    const service = await this.prisma.service.findUnique({
      where: {
        id: dto.serviceId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: service.description,
            },
            unit_amount: service.price * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        serviceId: service.id,
        userId: user.id,
        amount: service.price,
        type: 'mentor_service',
      },
      mode: 'payment', // One-time payment
      success_url: service.meeting_link, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }

  /**
   * Process Mentor Service Payment for Guest Users
   *
   * Creates a Stripe checkout session for mentor service purchases by guest users
   * (non-authenticated users). This allows users to purchase services without
   * creating an account first.
   *
   * Flow:
   * 1. Validates the service exists and is available
   * 2. Creates a Stripe checkout session with service details
   * 3. Includes guest user information in metadata
   * 4. Returns the checkout URL for user redirection
   *
   * @param dto - Payment request data containing serviceId and guest user details
   * @returns Promise<{status: string, url: string}> - Checkout session URL
   * @throws Error if service not found or Stripe session creation fails
   */
  async guest_service(dto: UserMentorServiceDto) {
    const service = await this.prisma.service.findUnique({
      where: {
        id: dto.serviceId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: service.description,
            },
            unit_amount: service.price * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        serviceId: service.id,
        amount: service.price,
        name: dto.name,
        email: dto.email,
        mobile_no: dto.mobile_no,
        type: 'guest-service',
      },
      mode: 'payment', // One-time payment
      success_url: service.meeting_link, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }
  async user_package(user: IJWTPayload, dto: UserPackageServiceDto) {
    const service = await this.prisma.packages.findUnique({
      where: {
        id: dto.packageId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: service.note,
            },
            unit_amount: service.amount * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        packageId: service.id,
        userId: user.id,
        amount: service.amount,
        type: 'package',
      },
      mode: 'payment', // One-time payment
      success_url: process.env.SUCCESS_URL, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }
  async guest_package(dto: UserPackageServiceDto) {
    const service = await this.prisma.packages.findUnique({
      where: {
        id: dto.packageId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: service.note,
            },
            unit_amount: service.amount * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        packageId: service.id,
        name: dto.name,
        email: dto.email,
        mobile_no: dto.mobile_no,
        amount: service.amount,
        type: 'guest-package',
      },
      mode: 'payment', // One-time payment
      success_url: process.env.SUCCESS_URL, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }
  async user_immigration(user: IJWTPayload, dto: UserImmigrationServiceDto) {
    const service = await this.prisma.immigration_service.findUnique({
      where: {
        id: dto.immigration_serviceId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: 'Visa Immigration Service',
            },
            unit_amount: service.amount * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        immigration_serviceId: service.id,
        userId: user.id,
        amount: service.amount,
        type: 'immigration',
      },
      mode: 'payment', // One-time payment
      success_url: process.env.SUCCESS_URL, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }
  async guest_immigration(dto: UserImmigrationServiceDto) {
    const service = await this.prisma.immigration_service.findUnique({
      where: {
        id: dto.immigration_serviceId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: 'Visa Immigration Service',
            },
            unit_amount: service.amount * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        immigration_serviceId: service.id,
        name: dto.name,
        email: dto.email,
        mobile_no: dto.mobile_no,
        amount: service.amount,
        type: 'guest-immigration',
      },
      mode: 'payment', // One-time payment
      success_url: process.env.SUCCESS_URL, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }
  async user_training(user: IJWTPayload, dto: UserTrainingServiceDto) {
    const service = await this.prisma.training.findUnique({
      where: {
        id: dto.trainingId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: 'Training Service',
            },
            unit_amount: service.amount * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        trainingId: service.id,
        userId: user.id,
        amount: service.amount,
        type: 'training',
      },
      mode: 'payment', // One-time payment
      success_url: process.env.SUCCESS_URL, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }
  async guest_training(dto: UserTrainingServiceDto) {
    const service = await this.prisma.training.findUnique({
      where: {
        id: dto.trainingId,
      },
    });
    const session = await this.stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: service.name,
              description: 'Training Service',
            },
            unit_amount: service.amount * 100, // Amount in smallest currency unit
          },
          quantity: 1,
        },
      ],
      metadata: {
        trainingId: service.id,
        name: dto.name,
        email: dto.email,
        mobile_no: dto.mobile_no,
        amount: service.amount,
        type: 'guest-training',
      },
      mode: 'payment', // One-time payment
      success_url: process.env.SUCCESS_URL, // Redirect after successful payment
      cancel_url: process.env.CANCELED_URL, // Redirect after canceled payment
    });

    return {
      status: 'OK',
      url: session.url,
    };
  }

  /**
   * Handle Stripe Webhook Events
   *
   * Processes webhook events from Stripe to handle payment confirmations and failures.
   * This method is called by Stripe when payment events occur, ensuring the database
   * is updated with the correct payment status and triggering email notifications.
   *
   * Supported Events:
   * - checkout.session.completed: Payment successful
   * - checkout.session.async_payment_failed: Payment failed
   *
   * Supported Payment Types:
   * - mentor_service / guest-service: Mentor consultation services
   * - package / guest-package: Service packages
   * - immigration / guest-immigration: Immigration services
   * - training / guest-training: Training programs
   *
   * Security:
   * - Verifies webhook signature using Stripe webhook secret
   * - Validates event authenticity before processing
   *
   * @param req - Raw HTTP request from Stripe containing webhook data
   * @returns Promise<{received: boolean}> - Acknowledgment of webhook receipt
   * @throws Error if webhook signature verification fails
   */
  async webhook(req: any) {
    const sig = req.headers['stripe-signature'];
    const event = this.stripe.webhooks.constructEvent(
      req.rawBody,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET,
    );
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        if (session.metadata.type === 'mentor_service') {
          await this.user_service({
            amount: Number(session.metadata.amount),
            serviceId: session.metadata.serviceId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-service') {
          await this.guestService({
            amount: Number(session.metadata.amount),
            serviceId: session.metadata.serviceId,
            status: session.payment_status,
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
            userId: '',
          });
        }
        if (session.metadata.type === 'package') {
          await this.package({
            amount: Number(session.metadata.amount),
            packageId: session.metadata.packageId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-package') {
          await this.guestPackage({
            amount: Number(session.metadata.amount),
            packageId: session.metadata.packageId,
            status: session.payment_status,
            userId: '',
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
          });
        }
        if (session.metadata.type === 'immigration') {
          await this.immigration({
            amount: Number(session.metadata.amount),
            immigration_serviceId: session.metadata.immigration_serviceId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-immigration') {
          await this.guestImmigration({
            amount: Number(session.metadata.amount),
            immigration_serviceId: session.metadata.immigration_serviceId,
            status: session.payment_status,
            userId: '',
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
          });
        }
        if (session.metadata.type === 'training') {
          await this.training({
            amount: Number(session.metadata.amount),
            trainingId: session.metadata.trainingId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-training') {
          await this.guestTraining({
            amount: Number(session.metadata.amount),
            trainingId: session.metadata.trainingId,
            status: session.payment_status,
            userId: '',
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
          });
        }
        break;
      case 'checkout.session.async_payment_failed':
        if (session.metadata.type === 'mentor_service') {
          await this.user_service({
            amount: Number(session.metadata.amount),
            serviceId: session.metadata.serviceId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-service') {
          await this.guestService({
            amount: Number(session.metadata.amount),
            serviceId: session.metadata.serviceId,
            status: session.payment_status,
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
            userId: '',
          });
        }
        if (session.metadata.type === 'package') {
          await this.package({
            amount: Number(session.metadata.amount),
            packageId: session.metadata.packageId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-package') {
          await this.guestPackage({
            amount: Number(session.metadata.amount),
            packageId: session.metadata.packageId,
            status: session.payment_status,
            userId: '',
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
          });
        }
        if (session.metadata.type === 'immigration') {
          await this.immigration({
            amount: Number(session.metadata.amount),
            immigration_serviceId: session.metadata.immigration_serviceId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-immigration') {
          await this.guestImmigration({
            amount: Number(session.metadata.amount),
            immigration_serviceId: session.metadata.immigration_serviceId,
            status: session.payment_status,
            userId: '',
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
          });
        }
        if (session.metadata.type === 'training') {
          await this.training({
            amount: Number(session.metadata.amount),
            trainingId: session.metadata.trainingId,
            status: session.payment_status,
            userId: session.metadata.userId,
          });
        }
        if (session.metadata.type === 'guest-training') {
          await this.guestTraining({
            amount: Number(session.metadata.amount),
            trainingId: session.metadata.trainingId,
            status: session.payment_status,
            userId: '',
            email: session.metadata.email,
            mobile_no: session.metadata.mobile_no,
            name: session.metadata.name,
          });
        }
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }
    return { received: true };
  }

  async user_service(data: UserServiceDto) {
    const service = await this.prisma.user_mentor_service.create({
      data,
      include: {
        mentor_services: {
          include: {
            mentor: true,
          },
        },
      },
    });

    const user = await this.prisma.user.findUnique({
      where: {
        id: service.userId,
      },
      select: {
        name: true,
        email: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: user.email,
        subject: 'Unlock Your Potential with Professional Mentorship',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              name: service?.mentor_services?.name || '',
              mentor: service?.mentor_services?.mentor?.name || '',
            },
            user,
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'Appointment booked with mentor',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Mentor Service',
            service: {
              ...service,
              name: service?.mentor_services?.name || '',
              mentor: service?.mentor_services?.mentor?.name || '',
            },
            user: {
              email: user.email,
              name: user.name,
            },
          }),
        ),
      });
    }

    return service;
  }
  async guestService(data: UserServiceDto) {
    const { userId, ...result } = data;
    const service = await this.prisma.guest_mentor_service.create({
      // @ts-ignore
      data: {
        ...result,
      },
      include: {
        mentor_services: {
          include: {
            mentor: true,
          },
        },
      },
    });

    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: service.email,
        subject: 'Unlock Your Potential with Professional Mentorship',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              // @ts-ignore
              name: service?.mentor_services?.name || '',
              // @ts-ignore
              mentor: service?.mentor_services?.mentor?.name || '',
            },
            user: { email: service.email, name: service.name },
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'Appointment booked with mentor',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Mentor Service',
            service: {
              ...service,
              // @ts-ignore
              name: service?.mentor_services?.name || '',
              // @ts-ignore
              mentor: service?.mentor_services?.mentor?.name || '',
            },
            user: {
              email: service.email,
              name: service.name,
              mobile_no: service.mobile_no,
            },
          }),
        ),
      });
    }

    return service;
  }
  async package(data: UserPackageDto) {
    const service = await this.prisma.user_package.create({
      data,
      include: {
        package: true,
      },
    });

    const user = await this.prisma.user.findUnique({
      where: {
        id: service.userId,
      },
      select: {
        name: true,
        email: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: user.email,
        subject: 'Explore Exclusive Packages Designed for You',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              name: service?.package?.name || '',
            },
            user,
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Mentorship Package Purchase',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Our Package',
            service: {
              ...service,
              name: service?.package?.name || '',
            },
            user: {
              email: user.email,
              name: user.name,
            },
          }),
        ),
      });
    }

    return service;
  }
  async guestPackage(data: UserPackageDto) {
    const { userId, ...result } = data;
    const service = await this.prisma.guest_package.create({
      // @ts-ignore
      data: {
        ...result,
      },
      include: {
        package: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: service.email,
        subject: 'Explore Exclusive Packages Designed for You',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              // @ts-ignore
              name: service?.package?.name || '',
            },
            user: { email: service.email, name: service.name },
          }),
        ),
      });

      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Mentorship Package Purchase',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Our Package',
            service: {
              ...service,
              // @ts-ignore
              name: service?.package?.name || '',
            },
            user: {
              email: data.email,
              name: data.name,
              mobile_no: data.mobile_no,
            },
          }),
        ),
      });
    }

    return service;
  }
  async immigration(data: UserImmigrationDto) {
    const service = await this.prisma.user_immigration_service.create({
      data,
      include: {
        immigration_service: true,
      },
    });

    const user = await this.prisma.user.findUnique({
      where: {
        id: service.userId,
      },
      select: {
        name: true,
        email: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: user.email,
        subject: 'Your Path to Global Opportunities Starts Here',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              name: service?.immigration_service?.name || '',
            },
            user,
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Immigration Services Purchase',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Immigration Service',
            service: {
              ...service,
              name: service?.immigration_service?.name || '',
            },
            user: {
              email: user.email,
              name: user.name,
            },
          }),
        ),
      });
    }

    return service;
  }
  async guestImmigration(data: UserImmigrationDto) {
    const { userId, ...result } = data;
    const service = await this.prisma.guest_immigration_service.create({
      // @ts-ignore
      data: {
        ...result,
      },
      include: {
        immigration_service: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: service.email,
        subject: 'Your Path to Global Opportunities Starts Here',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              // @ts-ignore
              name: service?.immigration_service?.name || '',
            },
            user: { email: service.email, name: service.name },
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Immigration Services Purchase',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Immigration Service',
            service: {
              ...service,
              // @ts-ignore
              name: service?.immigration_service?.name || '',
            },
            user: {
              email: data.email,
              name: data.name,
              mobile_no: data.mobile_no,
            },
          }),
        ),
      });
    }

    return service;
  }

  async training(data: UserTrainingDto) {
    const service = await this.prisma.user_training.create({
      data,
      include: {
        training: true,
      },
    });

    const user = await this.prisma.user.findUnique({
      where: {
        id: service.userId,
      },
      select: {
        name: true,
        email: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: user.email,
        subject: 'Grow your skills with our Training Program',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              name: service?.training?.name,
            },
            user,
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Training purchase',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Training Program',
            service: {
              ...service,
              name: service?.training?.name,
            },
            user: {
              email: user.email,
              name: user.name,
            },
          }),
        ),
      });
    }

    return service;
  }
  async guestTraining(data: UserTrainingDto) {
    const { userId, ...result } = data;
    const service = await this.prisma.guest_training.create({
      // @ts-ignore
      data: result,
      include: {
        training: true,
      },
    });
    if (service) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: service.email,
        subject: 'Grow your skills with our Training Program',
        cc: [],
        html: await render(
          MentorPaymentSuccessEmail({
            service: {
              ...service,
              // @ts-ignore
              name: service?.training?.name || '',
            },
            user: { email: service.email, name: service.name },
          }),
        ),
      });
      //admin notification
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Training purchase',
        cc: [],
        html: await render(
          PurchaseNotificationEmail({
            name: 'Training Program',
            service: {
              ...service,
              // @ts-ignore
              name: service?.training?.name || '',
            },
            user: {
              email: service.email,
              name: service.name,
              mobile_no: service.mobile_no,
            },
          }),
        ),
      });
    }

    return service;
  }
}
