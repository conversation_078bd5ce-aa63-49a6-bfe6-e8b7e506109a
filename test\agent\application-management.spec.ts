/**
 * Application Management Tests
 *
 * Tests for application priority and assignment management endpoints.
 * Tests admin-only priority updates and role-based assignment functionality.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { ApplicationModule } from '../../src/application/application.module';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { ApplicationStatus, PriorityLevel, AgentStatus } from '@prisma/client';

describe('Application Management Tests', () => {
  let app: INestApplication;
  let jwtService: JwtService;

  // Mock data
  const mockAdmin = {
    id: 'admin_123',
    name: 'Admin User',
    email: '<EMAIL>',
  };

  const mockAgent = {
    id: 'agent_123',
    name: '<PERSON>',
    email: '<EMAIL>',
    status: AgentStatus.Active,
  };

  const mockApplication = {
    id: 'app_123',
    application_number: 'IMM-2024-000001',
    service_type: 'immigration',
    status: ApplicationStatus.Draft,
    priority_level: PriorityLevel.Medium,
    assigned_to: null,
    user_id: 'user_123',
    created_at: new Date(),
    updated_at: new Date(),
  };

  // Mock services
  const mockPrismaService = {
    application: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    agent: {
      findUnique: jest.fn(),
    },
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ApplicationModule],
    })
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .overrideProvider(LoggerService)
      .useValue(mockLoggerService)
      .compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('PUT /applications/:id/priority', () => {
    const updatePriorityDto = {
      priority_level: 'High' as const,
    };

    it('should update application priority successfully for admin', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        priority_level: PriorityLevel.High,
      });

      const response = await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/priority`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatePriorityDto)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Application priority updated successfully',
        priority_level: 'High',
        applicationId: mockApplication.id,
      });

      expect(mockPrismaService.application.update).toHaveBeenCalledWith({
        where: { id: mockApplication.id },
        data: { priority_level: 'High' },
      });
    });

    it('should return 401 without admin token', async () => {
      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/priority`)
        .send(updatePriorityDto)
        .expect(401);
    });

    it('should return 401 with agent token', async () => {
      const agentToken = await jwtService.signAsync(
        { id: mockAgent.id, email: mockAgent.email, tokenType: 'agent' },
        { secret: process.env.jwtAgentSecretKey }
      );

      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/priority`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updatePriorityDto)
        .expect(401);
    });

    it('should return 404 if application not found', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.application.findUnique.mockResolvedValue(null);

      await request(app.getHttpServer())
        .put(`/applications/nonexistent/priority`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatePriorityDto)
        .expect(404);
    });

    it('should validate priority level values', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const invalidPriorityDto = {
        priority_level: 'Invalid' as any,
      };

      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/priority`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidPriorityDto)
        .expect(400);
    });
  });

  describe('PUT /applications/:id/assign', () => {
    const assignDto = {
      agentId: mockAgent.id,
    };

    it('should assign application to agent successfully for admin', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        assigned_to: mockAgent.id,
      });

      const response = await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(assignDto)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Application assigned successfully',
        assignedAgent: {
          id: mockAgent.id,
          name: mockAgent.name,
          email: mockAgent.email,
        },
        applicationId: mockApplication.id,
      });

      expect(mockPrismaService.application.update).toHaveBeenCalledWith({
        where: { id: mockApplication.id },
        data: { assigned_to: mockAgent.id },
      });
    });

    it('should allow agent to assign application to themselves', async () => {
      const agentToken = await jwtService.signAsync(
        { id: mockAgent.id, email: mockAgent.email, tokenType: 'agent' },
        { secret: process.env.jwtAgentSecretKey }
      );

      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockPrismaService.application.update.mockResolvedValue({
        ...mockApplication,
        assigned_to: mockAgent.id,
      });

      const response = await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/assign`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(assignDto)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Application assigned successfully',
        assignedAgent: {
          id: mockAgent.id,
          name: mockAgent.name,
          email: mockAgent.email,
        },
        applicationId: mockApplication.id,
      });
    });

    it('should prevent agent from assigning to another agent', async () => {
      const agentToken = await jwtService.signAsync(
        { id: mockAgent.id, email: mockAgent.email, tokenType: 'agent' },
        { secret: process.env.jwtAgentSecretKey }
      );

      const assignToOtherDto = {
        agentId: 'other_agent_id',
      };

      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/assign`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(assignToOtherDto)
        .expect(403);
    });

    it('should return 404 if application not found', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.application.findUnique.mockResolvedValue(null);

      await request(app.getHttpServer())
        .put(`/applications/nonexistent/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(assignDto)
        .expect(404);
    });

    it('should return 404 if agent not found', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const assignToNonexistentDto = {
        agentId: 'nonexistent_agent',
      };

      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.agent.findUnique.mockResolvedValue(null);

      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(assignToNonexistentDto)
        .expect(404);
    });

    it('should return 400 if agent is inactive', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const inactiveAgent = {
        ...mockAgent,
        status: AgentStatus.Inactive,
      };

      mockPrismaService.application.findUnique.mockResolvedValue(mockApplication);
      mockPrismaService.agent.findUnique.mockResolvedValue(inactiveAgent);

      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(assignDto)
        .expect(400);
    });

    it('should validate agentId format', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const invalidAgentDto = {
        agentId: 'invalid-uuid-format',
      };

      await request(app.getHttpServer())
        .put(`/applications/${mockApplication.id}/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidAgentDto)
        .expect(400);
    });
  });
});
