/**
 * Document Master Module
 *
 * This module provides document master CRUD functionality for the Career Ireland platform.
 * It includes controller, service, and all necessary dependencies for document master management.
 *
 * Key Features:
 * - Admin-only document master management
 * - Full CRUD operations with validation
 * - Usage checking before deletion
 * - Comprehensive error handling and logging
 * - Integration with existing authentication system
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { DocumentMasterService } from './document-master.service';
import { DocumentMasterController } from './document-master.controller';
import { PrismaService } from '../utils/prisma.service';

@Module({
  imports: [
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [DocumentMasterController],
  providers: [
    DocumentMasterService,
    PrismaService,
  ],
  exports: [DocumentMasterService],
})
export class DocumentMasterModule {}
