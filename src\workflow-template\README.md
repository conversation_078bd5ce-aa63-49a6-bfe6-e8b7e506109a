# Workflow Template CRUD Module

## Overview

The Workflow Template CRUD module provides comprehensive workflow template management functionality for the Career Ireland platform's Dynamic Workflow System. This module allows administrators to create, manage, and maintain detailed workflow templates that define the complete application process across all service types (immigration, training, packages, consulting).

## Features

### ✅ **Core Functionality**
- **Full CRUD Operations**: Create, Read, Update, Delete workflow templates
- **Admin-Only Access**: All operations restricted to admin users via `@JwtAdminGuard()`
- **Service-Agnostic Design**: Supports all service types (immigration, training, packages, consulting)
- **Usage Validation**: Prevents deletion of templates currently in use by active applications
- **Pagination & Filtering**: Efficient data retrieval with search capabilities
- **Audit Logging**: Tracks all operations with user attribution

### 🔒 **Security Features**
- JWT-based admin authentication
- Input validation and sanitization
- SQL injection protection via Prisma ORM
- Comprehensive error handling
- Audit trail for all operations

### 🚀 **Performance Features**
- Database indexing for optimal query performance
- Pagination for large datasets
- Efficient filtering and search capabilities
- Optimized Prisma queries

## Database Schema

### Workflow Template Table

```sql
CREATE TABLE workflow_template (
  id VARCHAR PRIMARY KEY DEFAULT cuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  service_type VARCHAR NOT NULL,
  service_id VARCHAR,
  is_active BOOLEAN DEFAULT true,
  workflow_template JSON NOT NULL,
  created_by VARCHAR,
  updated_by VARCHAR,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now()
);
```

### Workflow Template JSON Structure

```json
{
  "workflowTemplate": [
    {
      "stageName": "Document Collection",
      "stageOrder": 1,
      "description": "Collect all required documents",
      "estimatedDuration": 5,
      "documentsRequired": true,
      "documents": [
        {"documentName": "passport", "required": true},
        {"documentName": "visa", "required": true}
      ],
      "customFormRequired": true,
      "customForm": [
        {"fieldName": "name", "fieldType": "text", "required": true},
        {"fieldName": "age", "fieldType": "number", "required": true}
      ],
      "assigneeRole": "applicant",
      "autoAdvance": false
    }
  ]
}
```

### Indexes
- `name` - For efficient filtering and uniqueness checks
- `service_type` - For filtering by service type
- `is_active` - For filtering active/inactive templates
- `created_at` - For chronological ordering
- `updated_at` - For tracking recent changes

## Module Structure

```
src/workflow-template/
├── dto/
│   └── workflow-template.dto.ts        # Data Transfer Objects
├── interfaces/
│   └── workflow-template.interface.ts  # TypeScript interfaces
├── workflow-template.controller.ts     # REST API endpoints
├── workflow-template.service.ts        # Business logic
├── workflow-template.module.ts         # Module configuration
└── README.md                          # This documentation
```

## API Documentation

### Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/workflow-templates` | Create new workflow template | Admin |
| GET | `/workflow-templates` | Get paginated workflow templates | Admin |
| GET | `/workflow-templates/service-type/:serviceType` | Get templates by service type | Admin |
| GET | `/workflow-templates/:id` | Get workflow template by ID | Admin |
| GET | `/workflow-templates/:id/usage` | Check template usage | Admin |
| PATCH | `/workflow-templates/:id` | Update workflow template | Admin |

| DELETE | `/workflow-templates/:id` | Delete workflow template | Admin |

### Request/Response Examples

#### Create Workflow Template
```bash
POST /workflow-templates
Authorization: Bearer <admin-jwt-token>
Content-Type: application/json

{
  "name": "Standard Immigration Workflow",
  "description": "Complete workflow for immigration applications",
  "serviceType": "immigration",
  "serviceId": "immigration_service_123",
  "isActive": true,
  "workflowTemplate": [
    {
      "stageName": "Document Collection",
      "stageOrder": 1,
      "documentsRequired": true,
      "documents": [
        {"documentName": "passport", "required": true}
      ],
      "customFormRequired": true,
      "customForm": [
        {"fieldName": "name", "fieldType": "text", "required": true}
      ]
    }
  ]
}
```

#### Get Workflow Templates with Filters
```bash
GET /workflow-templates?serviceType=immigration&isActive=true&page=1&limit=10
Authorization: Bearer <admin-jwt-token>
```

#### Response Format
```json
{
  "data": [
    {
      "id": "clx1234567890abcdef",
      "name": "Standard Immigration Workflow",
      "description": "Complete workflow for immigration applications",
      "serviceType": "immigration",
      "serviceId": "immigration_service_123",
      "isActive": true,
      "workflowTemplate": [...],
      "createdBy": "Admin User",
      "updatedBy": null,
      "createdAt": "2025-01-06T10:30:00.000Z",
      "updatedAt": "2025-01-06T10:30:00.000Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10,
  "totalPages": 3
}
```

## Validation Rules

### Workflow Template Structure
- Must contain at least one stage
- Stage orders must be unique and sequential (1, 2, 3, ...)
- Each stage must have a valid `stageName` and `stageOrder`
- Document configurations must have `documentName` and `required` fields
- Custom form fields must have `fieldName`, `fieldType`, and `required` fields

### Service Types
- Must be one of: `immigration`, `training`, `packages`, `consulting`

### Field Validation
- `name`: Required, 1-255 characters, must be unique
- `description`: Optional, max 1000 characters
- `serviceType`: Required, valid enum value
- `serviceId`: Optional, max 100 characters
- `isActive`: Optional boolean, defaults to true

## Error Handling

### Common Error Responses

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| 400 | Bad Request | Invalid input data or workflow structure |
| 401 | Unauthorized | Missing or invalid admin authentication |
| 404 | Not Found | Workflow template not found |
| 409 | Conflict | Template name already exists or template in use |

### Example Error Response
```json
{
  "statusCode": 400,
  "message": "Workflow template stages must have unique stage orders",
  "error": "Bad Request"
}
```

## Testing

### Test Coverage
- **Target**: 95%+ test coverage
- **Unit Tests**: Service and controller logic
- **Integration Tests**: End-to-end API testing
- **Validation Tests**: Input validation and error handling

### Running Tests
```bash
# Run all workflow template tests
npm run test:workflow-template

# Run with coverage
npm run test:workflow-template -- --coverage

# Run specific test file
npm run test test/workflow-template/workflow-template.service.spec.ts
```

## Security

### Authentication & Authorization
- **Admin Only**: All endpoints require admin authentication
- **JWT Validation**: Uses `@JwtAdminGuard()` for secure access
- **Audit Logging**: Tracks all operations with user attribution

### Input Validation
- **DTO Validation**: Comprehensive input validation using class-validator
- **SQL Injection Protection**: Prisma ORM provides built-in protection
- **XSS Prevention**: Input sanitization and validation

## Performance Considerations

### Database Optimization
- Proper indexing on frequently queried fields
- Efficient pagination with skip/take
- Optimized Prisma queries with selective field loading

### Caching Strategy
- Consider implementing Redis caching for frequently accessed templates
- Cache invalidation on template updates

## Integration with Dynamic Workflow System

The Workflow Template module integrates with the existing Dynamic Workflow System:

1. **Template Management**: Provides detailed workflow templates for all service types
2. **Application Processing**: Templates are used to guide application workflows
3. **Usage Tracking**: Prevents deletion of templates in active use
4. **Service-Agnostic Design**: Works across all service types

## Future Enhancements

### Planned Features
- **Template Versioning**: Version control for workflow templates
- **Template Cloning**: Duplicate existing templates for quick setup
- **Bulk Operations**: Import/export templates in bulk
- **Template Analytics**: Usage statistics and performance metrics
- **Conditional Logic**: Advanced workflow branching based on conditions

### Migration Path
- Non-destructive updates maintaining backward compatibility
- Gradual feature rollout with feature flags
- Comprehensive testing before production deployment

## Troubleshooting

### Common Issues

1. **Template Creation Fails**
   - Check workflow template structure validation
   - Ensure stage orders are sequential
   - Verify admin authentication

2. **Template Deletion Blocked**
   - Check if template is in use by active applications
   - Use usage endpoint to see application dependencies

3. **Performance Issues**
   - Review database indexes
   - Check pagination parameters
   - Monitor query performance

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=debug` in environment variables.

## Support

For technical support or questions about the Workflow Template module:
- Review this documentation
- Check existing test cases for usage examples
- Contact the development team for assistance
