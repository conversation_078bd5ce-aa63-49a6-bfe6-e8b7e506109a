/**
 * Admin Notification Controller
 * Task 8: API Implementation and Documentation
 * 
 * Controller for admin notification management endpoints
 */

import {
  Controller,
  Post,
  Body,
  UseGuards,
  Logger,
  HttpStatus,
  HttpException,
  Get,
  Query,
  DefaultValuePipe,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAdmin } from '../../guards/jwt.admin.guard';
import { NotificationService } from '../services/notification.service';
import { SendNotificationDto, NotificationResponseDto } from '../dto/notification.dto';

/**
 * Admin Notification Controller
 * Provides endpoints for admin notification management
 */
@ApiTags('admin-notifications')
@Controller('admin/notifications')
export class AdminNotificationController {
  private readonly logger = new Logger(AdminNotificationController.name);

  constructor(
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * Send notification (admin only)
   */
  @Post()
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Send notification (Admin only)',
    description: 'Send immediate notification to users via email or SMS',
  })
  @ApiResponse({
    status: 201,
    description: 'Notification sent successfully',
    type: NotificationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid notification data',
  })
  @ApiResponse({
    status: 403,
    description: 'Admin access required',
  })
  async sendNotification(
    @Body() dto: SendNotificationDto,
  ): Promise<NotificationResponseDto> {
    try {
      this.logger.log(`Admin sending notification to: ${dto.recipient_email}`);

      const notification = await this.notificationService.sendNotification({
        notification_type: dto.notification_type,
        template_id: dto.template_id,
        recipient_user_id: dto.recipient_user_id,
        recipient_email: dto.recipient_email,
        recipient_mobile: dto.recipient_mobile,
        subject: dto.subject,
        message_body: dto.message_body,
        application_id: dto.application_id,
        document_id: dto.document_id,
        metadata: dto.metadata,
      });

      return {
        status: 'success',
        message: 'Notification sent successfully',
        data: {
          id: notification.id,
          notification_type: notification.notification_type,
          recipient_email: notification.recipient_email,
          subject: notification.subject,
          status: notification.status,
          sent_at: notification.sent_at,
          created_at: notification.created_at,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to send notification: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Schedule notification (admin only)
   */
  @Post('schedule')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Schedule notification (Admin only)',
    description: 'Schedule notification for future delivery',
  })
  @ApiResponse({
    status: 201,
    description: 'Notification scheduled successfully',
    type: NotificationResponseDto,
  })
  async scheduleNotification(
    @Body() dto: SendNotificationDto & { scheduled_at: string },
  ): Promise<NotificationResponseDto> {
    try {
      this.logger.log(`Admin scheduling notification for: ${dto.scheduled_at}`);

      const scheduledAt = new Date(dto.scheduled_at);
      if (scheduledAt <= new Date()) {
        throw new HttpException(
          'Scheduled time must be in the future',
          HttpStatus.BAD_REQUEST,
        );
      }

      const notification = await this.notificationService.scheduleNotification(
        {
          notification_type: dto.notification_type,
          template_id: dto.template_id,
          recipient_user_id: dto.recipient_user_id,
          recipient_email: dto.recipient_email,
          recipient_mobile: dto.recipient_mobile,
          subject: dto.subject,
          message_body: dto.message_body,
          application_id: dto.application_id,
          document_id: dto.document_id,
          metadata: dto.metadata,
        },
        scheduledAt,
      );

      return {
        status: 'success',
        message: 'Notification scheduled successfully',
        data: {
          id: notification.id,
          notification_type: notification.notification_type,
          recipient_email: notification.recipient_email,
          subject: notification.subject,
          status: notification.status,
          scheduled_at: notification.scheduled_at,
          created_at: notification.created_at,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to schedule notification: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to schedule notification: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Get notification statistics (admin only)
   */
  @Get('stats')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get notification statistics (Admin only)',
    description: 'Retrieve notification statistics and metrics',
  })
  @ApiQuery({
    name: 'application_id',
    required: false,
    description: 'Filter by application ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Notification statistics retrieved successfully',
  })
  async getNotificationStats(
    @Query('application_id') applicationId?: string,
  ) {
    try {
      this.logger.log('Admin requesting notification statistics');

      const stats = await this.notificationService.getNotificationStats(applicationId);

      return {
        status: 'success',
        message: 'Notification statistics retrieved successfully',
        data: stats,
      };
    } catch (error) {
      this.logger.error(`Failed to get notification stats: ${error.message}`, error.stack);
      throw new HttpException(
        'Failed to retrieve notification statistics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Send notification from template (admin only)
   */
  @Post('template')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Send notification from template (Admin only)',
    description: 'Send notification using predefined template with context variables',
  })
  @ApiResponse({
    status: 201,
    description: 'Notification sent from template successfully',
    type: NotificationResponseDto,
  })
  async sendNotificationFromTemplate(
    @Body() dto: { template_id: string; context: any },
  ): Promise<NotificationResponseDto> {
    try {
      this.logger.log(`Admin sending notification from template: ${dto.template_id}`);

      const notification = await this.notificationService.createNotificationFromTemplate(
        dto.template_id,
        dto.context,
      );

      return {
        status: 'success',
        message: 'Notification sent from template successfully',
        data: {
          id: notification.id,
          notification_type: notification.notification_type,
          recipient_email: notification.recipient_email,
          subject: notification.subject,
          status: notification.status,
          sent_at: notification.sent_at,
          created_at: notification.created_at,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to send notification from template: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to send notification from template: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
