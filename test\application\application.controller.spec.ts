/**
 * Simplified Application Controller Tests
 *
 * Tests for the simplified ApplicationController.
 * Ensures all endpoints work correctly with proper authentication.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ApplicationController } from '../../src/application/controllers/application.controller';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import {
  ApplicationResponseDto,
  ApplicationFiltersDto,
  CreateNewApplicationDto,
  CreateApplicationResponseDto,
} from '../../src/application/dto/application.dto';
import { IJWTPayload } from '../../src/types/auth';
import { ApplicationStatus, PriorityLevel } from '@prisma/client';

describe('ApplicationController', () => {
  let controller: ApplicationController;
  let applicationService: ApplicationService;

  const mockApplicationService = {
    getApplications: jest.fn(),
    getApplicationById: jest.fn(),
    validateUserAccess: jest.fn(),
    updateApplicationNote: jest.fn(),
    getApplicationsWithTransformation: jest.fn(),
    updateApplicationFormData: jest.fn(),
    updateEstimatedCompletion: jest.fn(),
    uploadApplicationDocument: jest.fn(),
    createNewApplication: jest.fn(),
  };

  const mockPrismaService = {
    application: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockUser: IJWTPayload = {
    id: 'user_123',
    email: '<EMAIL>',
    sub: {
      name: 'Test User',
    },
    iat: **********,
    exp: **********,
    tokenType: 'user',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicationController],
      providers: [
        {
          provide: ApplicationService,
          useValue: mockApplicationService,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    controller = module.get<ApplicationController>(ApplicationController);
    applicationService = module.get<ApplicationService>(ApplicationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createApplication', () => {
    const createApplicationDto: CreateNewApplicationDto = {
      service_type: 'immigration',
      service_id: 'service_123',
      user_id: 'user_456',
      priority_level: PriorityLevel.Medium,
      workflow_template_id: 'template_789',
      payments: ['payment_1', 'payment_2'],
      assigned_agent: ['agent_1', 'agent_2'],
    };

    it('should create application successfully', async () => {
      const mockCreatedApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_123',
        user_id: 'user_456',
        priority_level: PriorityLevel.Medium,
        workflow_template_id: 'template_789',
        payment_ids: ['payment_1', 'payment_2'],
        agent_ids: ['agent_1', 'agent_2'],
        status: ApplicationStatus.Draft,
        current_step: '1',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockApplicationService.createNewApplication.mockResolvedValue(
        mockCreatedApplication,
      );

      const result = await controller.createApplication(
        mockUser,
        createApplicationDto,
      );

      expect(mockApplicationService.createNewApplication).toHaveBeenCalledWith(
        createApplicationDto,
        mockUser.id,
      );
      expect(result).toEqual({
        success: true,
        message: 'Application created successfully',
        data: mockCreatedApplication,
      });
    });

    it('should handle authentication error', async () => {
      const unauthenticatedUser = { ...mockUser, id: undefined };

      await expect(
        controller.createApplication(unauthenticatedUser as any, createApplicationDto),
      ).rejects.toThrow(HttpException);
    });

    it('should handle service errors', async () => {
      mockApplicationService.createNewApplication.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(
        controller.createApplication(mockUser, createApplicationDto),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getUserApplications', () => {
    it('should return user applications with pagination', async () => {
      const mockApplications = [
        {
          id: 'app_1',
          application_number: 'IMM-2024-000001',
          service_type: 'immigration',
          status: ApplicationStatus.Draft,
          priority_level: PriorityLevel.Medium,
          current_step: '1',
          user: {
            id: 'user_123',
            name: 'Test User',
            email: '<EMAIL>',
          },
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      const mockResult = {
        applications: mockApplications,
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockApplicationService.getApplications.mockResolvedValue(mockResult);

      const filters = { page: 1, limit: 10 };
      const result = await controller.getUserApplications(mockUser, filters);

      expect(result).toEqual({
        applications: expect.arrayContaining([
          expect.objectContaining({
            id: 'app_1',
            application_number: 'IMM-2024-000001',
            service_type: 'immigration',
            status: ApplicationStatus.Draft,
            current_step: 1,
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });

      expect(mockApplicationService.getApplications).toHaveBeenCalledWith(
        { ...filters, user_id: 'user_123' },
        1,
        10,
      );
    });

    it('should handle service errors', async () => {
      mockApplicationService.getApplications.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        controller.getUserApplications(mockUser, {}),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getApplicationDetails', () => {
    it('should return application details for authorized user', async () => {
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        status: ApplicationStatus.Draft,
        priority_level: PriorityLevel.Medium,
        user_id: 'user_123',
        current_step: '1',
        user: {
          id: 'user_123',
          name: 'Test User',
          email: '<EMAIL>',
        },
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockApplicationService.getApplicationById.mockResolvedValue(
        mockApplication,
      );

      const result = await controller.getApplicationDetails(
        mockUser,
        'app_123',
      );

      expect(result).toEqual(
        expect.objectContaining({
          id: 'app_123',
          application_number: 'IMM-2024-000001',
          service_type: 'immigration',
          current_step: 1,
        }),
      );

      expect(mockApplicationService.getApplicationById).toHaveBeenCalledWith(
        'app_123',
      );
    });

    it('should throw forbidden error for unauthorized user', async () => {
      const mockApplication = {
        id: 'app_123',
        user_id: 'other_user',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        status: ApplicationStatus.Draft,
        priority_level: PriorityLevel.Medium,
        current_step: '1',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockApplicationService.getApplicationById.mockResolvedValue(
        mockApplication,
      );

      await expect(
        controller.getApplicationDetails(mockUser, 'app_123'),
      ).rejects.toThrow(
        new HttpException('Access denied', HttpStatus.FORBIDDEN),
      );
    });
  });

  describe('getAllApplications', () => {
    it('should return all applications for admin', async () => {
      const mockApplications = [
        {
          id: 'app_1',
          application_number: 'IMM-2024-000001',
          service_type: 'immigration',
          status: ApplicationStatus.Draft,
          priority_level: PriorityLevel.Medium,
          current_step: '1',
          user: {
            id: 'user_123',
            name: 'Test User',
            email: '<EMAIL>',
          },
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      const mockResult = {
        applications: mockApplications,
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockApplicationService.getApplications.mockResolvedValue(mockResult);

      const filters = { page: 1, limit: 10 };
      const result = await controller.getAllApplications(filters);

      expect(result).toEqual({
        applications: expect.arrayContaining([
          expect.objectContaining({
            id: 'app_1',
            application_number: 'IMM-2024-000001',
            service_type: 'immigration',
            current_step: 1,
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });

      expect(mockApplicationService.getApplications).toHaveBeenCalledWith(
        filters,
        1,
        10,
      );
    });
  });

  describe('updateApplicationNote', () => {
    it('should update application note successfully', async () => {
      const applicationId = 'app_123';
      const noteData = { note: 'Updated note content' };
      const mockUpdatedApplication = {
        id: applicationId,
        note: 'Updated note content',
        updated_at: new Date(),
      };

      mockApplicationService.validateUserAccess.mockResolvedValue(true);
      mockApplicationService.updateApplicationNote.mockResolvedValue(mockUpdatedApplication);

      const result = await controller.updateApplicationNote(
        mockUser,
        applicationId,
        noteData,
      );

      expect(result).toEqual({
        success: true,
        message: 'Application note updated successfully',
        note: 'Updated note content',
      });

      expect(mockApplicationService.validateUserAccess).toHaveBeenCalledWith(
        applicationId,
        mockUser.id,
      );
      expect(mockApplicationService.updateApplicationNote).toHaveBeenCalledWith(
        applicationId,
        'Updated note content',
      );
    });

    it('should throw forbidden error for unauthorized user', async () => {
      const applicationId = 'app_123';
      const noteData = { note: 'Updated note content' };

      mockApplicationService.validateUserAccess.mockResolvedValue(false);

      await expect(
        controller.updateApplicationNote(mockUser, applicationId, noteData),
      ).rejects.toThrow(
        new HttpException('Access denied', HttpStatus.FORBIDDEN),
      );

      expect(mockApplicationService.validateUserAccess).toHaveBeenCalledWith(
        applicationId,
        mockUser.id,
      );
      expect(mockApplicationService.updateApplicationNote).not.toHaveBeenCalled();
    });

    it('should handle agent access control', async () => {
      const agentUser: IJWTPayload = {
        ...mockUser,
        tokenType: 'agent',
      };
      const applicationId = 'app_123';
      const noteData = { note: 'Agent note' };
      const mockApplication = {
        id: applicationId,
        assigned_to: agentUser.id,
      };
      const mockUpdatedApplication = {
        id: applicationId,
        note: 'Agent note',
        updated_at: new Date(),
      };

      mockApplicationService.getApplicationById.mockResolvedValue(mockApplication);
      mockApplicationService.updateApplicationNote.mockResolvedValue(mockUpdatedApplication);

      const result = await controller.updateApplicationNote(
        agentUser,
        applicationId,
        noteData,
      );

      expect(result).toEqual({
        success: true,
        message: 'Application note updated successfully',
        note: 'Agent note',
      });

      expect(mockApplicationService.getApplicationById).toHaveBeenCalledWith(applicationId);
      expect(mockApplicationService.updateApplicationNote).toHaveBeenCalledWith(
        applicationId,
        'Agent note',
      );
    });

    it('should throw forbidden error for agent not assigned to application', async () => {
      const agentUser: IJWTPayload = {
        ...mockUser,
        tokenType: 'agent',
      };
      const applicationId = 'app_123';
      const noteData = { note: 'Agent note' };
      const mockApplication = {
        id: applicationId,
        assigned_to: 'other_agent_id',
      };

      mockApplicationService.getApplicationById.mockResolvedValue(mockApplication);

      await expect(
        controller.updateApplicationNote(agentUser, applicationId, noteData),
      ).rejects.toThrow(
        new HttpException('Access denied', HttpStatus.FORBIDDEN),
      );

      expect(mockApplicationService.getApplicationById).toHaveBeenCalledWith(applicationId);
      expect(mockApplicationService.updateApplicationNote).not.toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      const applicationId = 'app_123';
      const noteData = { note: 'Updated note content' };

      mockApplicationService.validateUserAccess.mockResolvedValue(true);
      mockApplicationService.updateApplicationNote.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        controller.updateApplicationNote(mockUser, applicationId, noteData),
      ).rejects.toThrow(HttpException);

      expect(mockApplicationService.validateUserAccess).toHaveBeenCalledWith(
        applicationId,
        mockUser.id,
      );
      expect(mockApplicationService.updateApplicationNote).toHaveBeenCalledWith(
        applicationId,
        'Updated note content',
      );
    });
  });
});
