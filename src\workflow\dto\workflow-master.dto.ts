/**
 * Workflow Master Data Transfer Objects (DTOs)
 *
 * This file contains all DTOs for workflow master operations including
 * validation rules, API documentation, and response structures.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  Min,
  Max,
  Length,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

/**
 * Create Workflow Master DTO
 *
 * DTO for creating a new workflow master template.
 * Contains all required fields for workflow master creation.
 */
export class CreateWorkflowMasterDto {
  @ApiProperty({
    description: 'Workflow template name',
    example: 'Standard Immigration Application Workflow',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  @ApiPropertyOptional({
    description: 'Workflow description',
    example: 'Complete workflow template for immigration applications',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Whether this workflow master is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}

/**
 * Update Workflow Master DTO
 *
 * DTO for updating an existing workflow master.
 * All fields are optional for partial updates.
 */
export class UpdateWorkflowMasterDto extends PartialType(CreateWorkflowMasterDto) {}

/**
 * Workflow Master Filters DTO
 *
 * DTO for filtering and pagination of workflow masters.
 */
export class WorkflowMasterFiltersDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Search term for workflow name',
    example: 'immigration',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by active status',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'created_at',
    enum: ['name', 'created_at', 'updated_at'],
    default: 'created_at',
  })
  @IsOptional()
  @IsString()
  sort_by?: string = 'created_at';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsString()
  sort_order?: 'asc' | 'desc' = 'desc';
}

/**
 * Workflow Master Response DTO
 *
 * DTO for workflow master API responses.
 * Includes all workflow master fields plus audit information.
 */
export class WorkflowMasterResponseDto {
  @ApiProperty({
    description: 'Workflow master unique identifier',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Workflow template name',
    example: 'Standard Immigration Application Workflow',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Workflow description',
    example: 'Complete workflow template for immigration applications',
  })
  description?: string;

  @ApiProperty({
    description: 'Whether this workflow master is active',
    example: true,
  })
  is_active: boolean;

  @ApiPropertyOptional({
    description: 'ID of user who created this workflow master',
    example: 'clx0987654321fedcba',
  })
  created_by?: string;

  @ApiPropertyOptional({
    description: 'ID of user who last updated this workflow master',
    example: 'clx0987654321fedcba',
  })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-01-06T10:30:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-01-06T15:45:00.000Z',
  })
  updated_at: Date;
}

/**
 * Paginated Workflow Master Response DTO
 *
 * DTO for paginated workflow master responses.
 */
export class PaginatedWorkflowMasterResponseDto {
  @ApiProperty({
    description: 'Array of workflow masters',
    type: [WorkflowMasterResponseDto],
  })
  data: WorkflowMasterResponseDto[];

  @ApiProperty({
    description: 'Total number of workflow masters',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}

/**
 * Workflow Master Usage Response DTO
 *
 * DTO for workflow master usage information.
 */
export class WorkflowMasterUsageResponseDto {
  @ApiProperty({
    description: 'Workflow master ID',
    example: 'clx1234567890abcdef',
  })
  workflow_master_id: string;

  @ApiProperty({
    description: 'Total usage count',
    example: 15,
  })
  usage_count: number;

  @ApiProperty({
    description: 'Detailed usage breakdown',
    example: {
      applications: 10,
      templates: 3,
      active_workflows: 2,
    },
  })
  usage_details: {
    applications?: number;
    templates?: number;
    active_workflows?: number;
  };
}
