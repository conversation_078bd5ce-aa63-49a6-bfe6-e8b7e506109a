import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import * as React from 'react';

interface EmailVerificationProps {
  otp: string;
  userName?: string;
  verificationUrl: string;
}

export default function EmailVerification({
  otp = '000000',
  userName = '',
  verificationUrl = 'https://example.com/verify',
}: EmailVerificationProps) {
  return (
    <Html>
      <Head />
      <Preview>Verify your email address</Preview>
      <Body style={main}>
        <Container style={container}>
          <Container style={logoSection}>
            <Link href={process.env.WEBSITE} style={logoLink}>
              <Img
                src="https://supabase.careerireland.com/storage/v1/object/public/careerireland-prod/logo/logo-careerireland.webp"
                width="80"
                height="80"
                alt="Logo"
                style={logoImage}
              />
            </Link>
          </Container>
          <Heading style={h1}>Verify your email address</Heading>

          <Text style={text}>{userName ? `Hi ${userName},` : 'Hello,'}</Text>

          <Text style={text}>
            Please use the verification code below to verify your email address:
          </Text>

          <Section style={codeContainer}>
            <Text style={code}>{otp}</Text>
          </Section>

          <Text style={text}>
            You can click the button below to verify your email address by
            entering the code above
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={verificationUrl}>
              Verify Email Address
            </Button>
          </Section>

          <Text style={text}>
            This verification code and link will expire in 10 minutes.
          </Text>

          <Text style={text}>
            If you didn't request this verification, you can safely ignore this
            email. Someone might have typed your email address by mistake.
          </Text>

          <Text style={text}>
            Thanks,
            <br />
            The Careerireland
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  padding: '0 48px',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '24px auto',
  padding: '48px 24px',
  borderRadius: '8px',
  maxWidth: '500px',
};

const logoLink = {
  display: 'inline-block',
  textDecoration: 'none',
};

const logoSection = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  maxWidth: '600px',
};

const logoImage = {
  margin: '0 auto',
  borderRadius: '12px',
  padding: '12px',
  backgroundColor: '#f8fafc',
  objectFit: 'contain' as const,
  transition: 'all 0.2s ease',
};
const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '1.4',
  margin: '0 0 24px',
  textAlign: 'center' as const,
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '0 auto',
};

const codeContainer = {
  background: '#f3f4f6',
  borderRadius: '4px',
  margin: '36px 0',
  padding: '32px 24px',
};

const code = {
  color: '#1f2937',
  fontSize: '36px',
  fontWeight: '700',
  letterSpacing: '0.25em',
  lineHeight: '1',
  margin: '0',
  textAlign: 'center' as const,
  fontFamily: 'monospace',
};
