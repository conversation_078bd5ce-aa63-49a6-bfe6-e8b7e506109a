/**
 * User Controller Unit Tests
 *
 * Tests for user registration and authentication endpoints.
 * Covers all scenarios including optional password and mobile number support.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException } from '@nestjs/common';
import { UserController } from '../../src/user/user.controller';
import { UserService } from '../../src/user/user.service';
import {
  CreateUserDto,
  LoginDto,
  UpdateUserDto,
} from '../../src/user/dto/user.dto';
import { IJWTPayload } from '../../src/types/auth';

describe('UserController', () => {
  let controller: UserController;
  let userService: jest.Mocked<UserService>;

  const mockUserService = {
    create: jest.fn(),
    login: jest.fn(),
    findById: jest.fn(),
    update: jest.fn(),
    removeAccount: jest.fn(),
  };

  const mockUser: IJWTPayload = {
    id: 'user_123',
    email: '<EMAIL>',
    sub: {
      name: 'Test User',
    },
    iat: **********,
    exp: **********,
    tokenType: 'user',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    userService = module.get(UserService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('registerUser (POST /user/register)', () => {
    const baseUserDto: CreateUserDto = {
      name: 'John Doe',
      email: '<EMAIL>',
    };

    const mockRegistrationResponse = {
      token: 'otp-token-123',
      status: 'Ok',
      message: 'Verify your email address',
    };

    it('should register user successfully with password and mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
        mobileNo: '+353-1-234-5678',
      };

      mockUserService.create.mockResolvedValue(mockRegistrationResponse);

      const result = await controller.registerUser(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(mockRegistrationResponse);
    });

    it('should register user successfully without password but with mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        mobileNo: '+353-1-234-5678',
      };

      const mockPasswordlessResponse = {
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required for password-less registration.',
        user: {
          id: 'user_123',
          name: 'John Doe',
          email: '<EMAIL>',
          mobile_no: '+353-1-234-5678',
          emailVerified: false,
        },
      };

      mockUserService.create.mockResolvedValue(mockPasswordlessResponse);

      const result = await controller.registerUser(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(mockPasswordlessResponse);
    });

    it('should register user successfully with password but without mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
      };

      mockUserService.create.mockResolvedValue(mockRegistrationResponse);

      const result = await controller.registerUser(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(mockRegistrationResponse);
    });

    it('should register user successfully without password and without mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
      };

      const mockPasswordlessResponse = {
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required for password-less registration.',
        user: {
          id: 'user_123',
          name: 'John Doe',
          email: '<EMAIL>',
          mobile_no: null,
          emailVerified: false,
        },
      };

      mockUserService.create.mockResolvedValue(mockPasswordlessResponse);

      const result = await controller.registerUser(createUserDto);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(mockPasswordlessResponse);
    });

    it('should handle ConflictException when email already exists', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
      };

      mockUserService.create.mockRejectedValue(
        new ConflictException('User Email already exists'),
      );

      await expect(controller.registerUser(createUserDto)).rejects.toThrow(
        ConflictException,
      );
      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
    });

    it('should handle service errors gracefully', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
      };

      mockUserService.create.mockRejectedValue(
        new Error('Database connection failed'),
      );

      await expect(controller.registerUser(createUserDto)).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('login (POST /user/login)', () => {
    const loginDto: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const mockLoginResponse = {
      user: {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        mobile_no: '+353-1-234-5678',
        emailVerified: true,
      },
      backendTokens: {
        accessToken: 'access_token_123',
        refreshToken: 'refresh_token_123',
        expiresIn: **********,
      },
    };

    it('should login user successfully', async () => {
      mockUserService.login.mockResolvedValue(mockLoginResponse);

      const result = await controller.login(loginDto);

      expect(mockUserService.login).toHaveBeenCalledWith(loginDto);
      expect(result).toEqual(mockLoginResponse);
    });

    it('should handle login errors', async () => {
      mockUserService.login.mockRejectedValue(new Error('Invalid credentials'));

      await expect(controller.login(loginDto)).rejects.toThrow(
        'Invalid credentials',
      );
    });
  });

  describe('getUserProfile (GET /user)', () => {
    const mockUserProfile = {
      id: 'user_123',
      name: 'John Doe',
      email: '<EMAIL>',
      mobile_no: '+353-1-234-5678',
      emailVerified: true,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should get user profile successfully', async () => {
      mockUserService.findById.mockResolvedValue(mockUserProfile);

      const result = await controller.getUserProfile(mockUser);

      expect(mockUserService.findById).toHaveBeenCalledWith(mockUser);
      expect(result).toEqual(mockUserProfile);
    });

    it('should handle profile retrieval errors', async () => {
      mockUserService.findById.mockRejectedValue(new Error('User not found'));

      await expect(controller.getUserProfile(mockUser)).rejects.toThrow(
        'User not found',
      );
    });
  });

  describe('update (PATCH /user)', () => {
    const updateUserDto: UpdateUserDto = {
      name: 'John Updated',
      email: '<EMAIL>',
      password: 'newpassword123',
      image: 'new-image.jpg',
      mobile_no: '+353-1-999-8888',
    };

    const mockUpdatedUser = {
      id: 'user_123',
      name: 'John Updated',
      email: '<EMAIL>',
      mobile_no: '+353-1-999-8888',
      emailVerified: true,
      updatedAt: new Date(),
    };

    it('should update user successfully', async () => {
      mockUserService.update.mockResolvedValue(mockUpdatedUser);

      const result = await controller.update(mockUser, updateUserDto);

      expect(mockUserService.update).toHaveBeenCalledWith(
        mockUser,
        updateUserDto,
      );
      expect(result).toEqual(mockUpdatedUser);
    });

    it('should handle update errors', async () => {
      mockUserService.update.mockRejectedValue(new Error('Update failed'));

      await expect(controller.update(mockUser, updateUserDto)).rejects.toThrow(
        'Update failed',
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle unexpected service errors', async () => {
      const createUserDto: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
      };

      mockUserService.create.mockRejectedValue(new Error('Unexpected error'));

      await expect(controller.registerUser(createUserDto)).rejects.toThrow(
        'Unexpected error',
      );
    });

    it('should pass through service responses without modification', async () => {
      const createUserDto: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
        mobile_no: '+353-1-234-5678',
      };

      const mockResponse = {
        token: 'custom-token',
        status: 'Success',
        message: 'Custom message',
        additionalData: { customField: 'value' },
      };

      mockUserService.create.mockResolvedValue(mockResponse);

      const result = await controller.registerUser(createUserDto);

      expect(result).toEqual(mockResponse);
      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
    });
  });
});
