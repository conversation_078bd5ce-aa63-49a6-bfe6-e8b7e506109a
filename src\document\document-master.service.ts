/**
 * Document Master Service
 *
 * This service provides business logic for document master CRUD operations.
 * It handles validation, audit logging, and usage checking for document masters.
 *
 * Key Features:
 * - Full CRUD operations for document masters
 * - Usage validation before deletion
 * - Audit logging for all operations
 * - Pagination and filtering support
 * - Error handling and logging
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import {
  CreateDocumentMasterDto,
  UpdateDocumentMasterDto,
  DocumentMasterFiltersDto,
  PaginatedDocumentMasterResponseDto,
  DocumentMasterResponseDto,
  DocumentMasterUsageResponseDto,
} from './dto/document-master.dto';
import {
  IDocumentMaster,
  IDocumentMasterService,
  IDocumentMasterUsage,
  IPaginatedDocumentMasters,
} from './interfaces/document-master.interface';

@Injectable()
export class DocumentMasterService implements IDocumentMasterService {
  private readonly logger = new Logger(DocumentMasterService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new document master
   */
  async create(
    dto: CreateDocumentMasterDto,
    userId?: string,
  ): Promise<DocumentMasterResponseDto> {
    try {
      this.logger.log(`Creating document master: ${dto.name}`);

      // Check if document master with same name already exists
      const existingDocumentMaster =
        await this.prisma.document_master.findFirst({
          where: {
            name: {
              equals: dto.name,
              mode: 'insensitive',
            },
          },
        });

      if (existingDocumentMaster) {
        throw new ConflictException(
          `Document master with name "${dto.name}" already exists`,
        );
      }

      const documentMaster = await this.prisma.document_master.create({
        data: {
          ...dto,
          created_by: userId,
        },
      });

      this.logger.log(
        `Document master created successfully: ${documentMaster.id}`,
      );
      return documentMaster;
    } catch (error) {
      this.logger.error(
        `Failed to create document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get all document masters with optional filtering and pagination
   */
  async findAll(
    filters: DocumentMasterFiltersDto,
  ): Promise<PaginatedDocumentMasterResponseDto> {
    try {
      const page = filters.page || 1;
      const limit = Math.min(filters.limit || 10, 100); // Cap at 100 items per page
      const skip = (page - 1) * limit;

      this.logger.log(
        `Fetching document masters - Page: ${page}, Limit: ${limit}`,
      );

      // Build where clause for filtering
      const where: any = {};

      if (filters.category) {
        where.category = {
          contains: filters.category,
          mode: 'insensitive',
        };
      }

      // Task 4: Removed document_type filter - following non-destructive patterns
      // if (filters.document_type) {
      //   where.document_type = {
      //     contains: filters.document_type,
      //     mode: 'insensitive',
      //   };
      // }

      if (filters.search) {
        where.OR = [
          {
            name: {
              contains: filters.search,
              mode: 'insensitive',
            },
          },
          {
            description: {
              contains: filters.search,
              mode: 'insensitive',
            },
          },
        ];
      }

      // Get total count and data
      const [total, documentMasters] = await Promise.all([
        this.prisma.document_master.count({ where }),
        this.prisma.document_master.findMany({
          where,
          skip,
          take: limit,
          orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      this.logger.log(`Found ${total} document masters`);

      return {
        data: documentMasters,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch document masters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get a specific document master by ID
   */
  async findOne(id: string): Promise<DocumentMasterResponseDto> {
    try {
      this.logger.log(`Fetching document master: ${id}`);

      const documentMaster = await this.prisma.document_master.findUnique({
        where: { id },
      });

      if (!documentMaster) {
        throw new NotFoundException(
          `Document master with ID "${id}" not found`,
        );
      }

      this.logger.log(`Document master found: ${documentMaster.name}`);
      return documentMaster;
    } catch (error) {
      this.logger.error(
        `Failed to fetch document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update a document master
   */
  async update(
    id: string,
    dto: UpdateDocumentMasterDto,
    userId?: string,
  ): Promise<DocumentMasterResponseDto> {
    try {
      this.logger.log(`Updating document master: ${id}`);

      // Check if document master exists
      const existingDocumentMaster =
        await this.prisma.document_master.findUnique({
          where: { id },
        });

      if (!existingDocumentMaster) {
        throw new NotFoundException(
          `Document master with ID "${id}" not found`,
        );
      }

      // Check if name is being updated and if it conflicts with existing names
      if (dto.name && dto.name !== existingDocumentMaster.name) {
        const nameConflict = await this.prisma.document_master.findFirst({
          where: {
            name: {
              equals: dto.name,
              mode: 'insensitive',
            },
            id: {
              not: id,
            },
          },
        });

        if (nameConflict) {
          throw new ConflictException(
            `Document master with name "${dto.name}" already exists`,
          );
        }
      }

      const updatedDocumentMaster = await this.prisma.document_master.update({
        where: { id },
        data: {
          ...dto,
          updated_by: userId,
        },
      });

      this.logger.log(
        `Document master updated successfully: ${updatedDocumentMaster.id}`,
      );
      return updatedDocumentMaster;
    } catch (error) {
      this.logger.error(
        `Failed to update document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a document master (with usage validation)
   */
  async remove(id: string): Promise<void> {
    try {
      this.logger.log(`Attempting to delete document master: ${id}`);

      // Check if document master exists
      const documentMaster = await this.prisma.document_master.findUnique({
        where: { id },
      });

      if (!documentMaster) {
        throw new NotFoundException(
          `Document master with ID "${id}" not found`,
        );
      }

      // Check if document master is in use
      const usage = await this.checkUsage(id);
      if (usage.inUse) {
        throw new ConflictException(
          `Cannot delete document master "${documentMaster.name}" as it is currently in use by ${usage.usageCount} application(s)`,
        );
      }

      await this.prisma.document_master.delete({
        where: { id },
      });

      this.logger.log(`Document master deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete document master: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check if a document master is in use
   */
  async checkUsage(id: string): Promise<DocumentMasterUsageResponseDto> {
    try {
      this.logger.log(`Checking usage for document master: ${id}`);

      // Note: This is a placeholder implementation
      // In a real implementation, you would check against actual application_document
      // or other tables that reference document masters

      // For now, we'll simulate usage checking
      // TODO: Implement actual usage checking against application tables
      const usageCount = 0; // Placeholder
      const usageDetails: string[] = []; // Placeholder

      const usage: DocumentMasterUsageResponseDto = {
        inUse: usageCount > 0,
        usageCount,
        usageDetails: usageCount > 0 ? usageDetails : undefined,
      };

      this.logger.log(
        `Usage check completed for document master: ${id} - In use: ${usage.inUse}`,
      );
      return usage;
    } catch (error) {
      this.logger.error(
        `Failed to check document master usage: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
