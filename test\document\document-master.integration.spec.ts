/**
 * Document Master Integration Tests
 *
 * Integration test suite for DocumentMaster module covering end-to-end functionality
 * with real database interactions and complete request/response cycles.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../src/utils/prisma.service';
import { DocumentMasterModule } from '../../src/document/document-master.module';
import { JwtService } from '@nestjs/jwt';

describe('DocumentMaster Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let adminToken: string;

  // Test data
  const testDocumentMaster = {
    name: 'Test Passport Copy',
    description: 'A test document master for integration testing',
    category: 'Test Identity Documents',
    document_type: 'Test Government ID',
    instructions: 'Test instructions for document submission.',
  };

  const updatedDocumentMaster = {
    name: 'Updated Test Passport Copy',
    description: 'Updated test description',
    category: 'Updated Test Category',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [DocumentMasterModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    
    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate admin JWT token for testing
    adminToken = jwtService.sign(
      {
        id: 'test-admin-id',
        email: '<EMAIL>',
        sub: { name: 'Test Admin' },
      },
      { secret: process.env.jwtAdminSecretKey || 'test-admin-secret' },
    );

    await app.init();
  });

  afterAll(async () => {
    // Clean up test data
    await prismaService.document_master.deleteMany({
      where: {
        name: {
          contains: 'Test',
        },
      },
    });

    await app.close();
  });

  beforeEach(async () => {
    // Clean up any existing test data before each test
    await prismaService.document_master.deleteMany({
      where: {
        name: {
          contains: 'Test',
        },
      },
    });
  });

  describe('POST /document-master', () => {
    it('should create a new document master', async () => {
      const response = await request(app.getHttpServer())
        .post('/document-master')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(testDocumentMaster)
        .expect(201);

      expect(response.body).toMatchObject({
        name: testDocumentMaster.name,
        description: testDocumentMaster.description,
        category: testDocumentMaster.category,
        document_type: testDocumentMaster.document_type,
        instructions: testDocumentMaster.instructions,
      });
      expect(response.body.id).toBeDefined();
      expect(response.body.created_at).toBeDefined();
      expect(response.body.updated_at).toBeDefined();
    });

    it('should return 409 for duplicate document master name', async () => {
      // Create first document master
      await request(app.getHttpServer())
        .post('/document-master')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(testDocumentMaster)
        .expect(201);

      // Try to create duplicate
      await request(app.getHttpServer())
        .post('/document-master')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(testDocumentMaster)
        .expect(409);
    });

    it('should return 400 for invalid input data', async () => {
      const invalidData = {
        name: '', // Empty name should fail validation
        category: 'Test Category',
        document_type: 'Test Type',
      };

      await request(app.getHttpServer())
        .post('/document-master')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidData)
        .expect(400);
    });

    it('should return 401 without admin token', async () => {
      await request(app.getHttpServer())
        .post('/document-master')
        .send(testDocumentMaster)
        .expect(401);
    });
  });

  describe('GET /document-master', () => {
    beforeEach(async () => {
      // Create test data
      await prismaService.document_master.create({
        data: testDocumentMaster,
      });
    });

    it('should return paginated document masters', async () => {
      const response = await request(app.getHttpServer())
        .get('/document-master')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('document_masters');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body).toHaveProperty('totalPages');
      expect(response.body.document_masters).toHaveLength(1);
      expect(response.body.document_masters[0]).toMatchObject({
        name: testDocumentMaster.name,
        category: testDocumentMaster.category,
      });
    });

    it('should filter by category', async () => {
      const response = await request(app.getHttpServer())
        .get('/document-master')
        .query({ category: 'Test Identity Documents' })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.document_masters).toHaveLength(1);
    });

    it('should filter by search term', async () => {
      const response = await request(app.getHttpServer())
        .get('/document-master')
        .query({ search: 'passport' })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.document_masters).toHaveLength(1);
    });

    it('should handle pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/document-master')
        .query({ page: 1, limit: 5 })
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.page).toBe(1);
      expect(response.body.limit).toBe(5);
    });
  });

  describe('GET /document-master/categories', () => {
    beforeEach(async () => {
      await prismaService.document_master.create({
        data: testDocumentMaster,
      });
    });

    it('should return unique categories', async () => {
      const response = await request(app.getHttpServer())
        .get('/document-master/categories')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toContain('Test Identity Documents');
    });
  });

  describe('GET /document-master/document-types', () => {
    beforeEach(async () => {
      await prismaService.document_master.create({
        data: testDocumentMaster,
      });
    });

    it('should return unique document types', async () => {
      const response = await request(app.getHttpServer())
        .get('/document-master/document-types')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toContain('Test Government ID');
    });
  });

  describe('GET /document-master/:id', () => {
    let documentMasterId: string;

    beforeEach(async () => {
      const created = await prismaService.document_master.create({
        data: testDocumentMaster,
      });
      documentMasterId = created.id;
    });

    it('should return a specific document master', async () => {
      const response = await request(app.getHttpServer())
        .get(`/document-master/${documentMasterId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: documentMasterId,
        name: testDocumentMaster.name,
        category: testDocumentMaster.category,
      });
    });

    it('should return 404 for non-existent document master', async () => {
      await request(app.getHttpServer())
        .get('/document-master/nonexistent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('GET /document-master/:id/usage', () => {
    let documentMasterId: string;

    beforeEach(async () => {
      const created = await prismaService.document_master.create({
        data: testDocumentMaster,
      });
      documentMasterId = created.id;
    });

    it('should return usage information', async () => {
      const response = await request(app.getHttpServer())
        .get(`/document-master/${documentMasterId}/usage`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('inUse');
      expect(response.body).toHaveProperty('usageCount');
      expect(typeof response.body.inUse).toBe('boolean');
      expect(typeof response.body.usageCount).toBe('number');
    });
  });

  describe('PATCH /document-master/:id', () => {
    let documentMasterId: string;

    beforeEach(async () => {
      const created = await prismaService.document_master.create({
        data: testDocumentMaster,
      });
      documentMasterId = created.id;
    });

    it('should update a document master', async () => {
      const response = await request(app.getHttpServer())
        .patch(`/document-master/${documentMasterId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatedDocumentMaster)
        .expect(200);

      expect(response.body).toMatchObject({
        id: documentMasterId,
        name: updatedDocumentMaster.name,
        description: updatedDocumentMaster.description,
        category: updatedDocumentMaster.category,
      });
    });

    it('should return 404 for non-existent document master', async () => {
      await request(app.getHttpServer())
        .patch('/document-master/nonexistent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updatedDocumentMaster)
        .expect(404);
    });
  });

  describe('DELETE /document-master/:id', () => {
    let documentMasterId: string;

    beforeEach(async () => {
      const created = await prismaService.document_master.create({
        data: testDocumentMaster,
      });
      documentMasterId = created.id;
    });

    it('should delete a document master', async () => {
      const response = await request(app.getHttpServer())
        .delete(`/document-master/${documentMasterId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toEqual({
        message: 'Document master deleted successfully',
      });

      // Verify deletion
      const deletedDocumentMaster = await prismaService.document_master.findUnique({
        where: { id: documentMasterId },
      });
      expect(deletedDocumentMaster).toBeNull();
    });

    it('should return 404 for non-existent document master', async () => {
      await request(app.getHttpServer())
        .delete('/document-master/nonexistent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });
});
