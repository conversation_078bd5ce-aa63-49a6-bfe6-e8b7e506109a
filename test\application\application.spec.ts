/**
 * Application Module Tests
 * Moved from scripts/test-task2-implementation.ts and enhanced for proper testing
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationModule } from '../../src/application/application.module';
// import { WorkflowEngineService } from '../../src/application/services/workflow-engine.service'; // REMOVED: Service not implemented
import { NotificationService } from '../../src/application/services/notification.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { MailerService } from '../../src/mailer/mailer.service';

describe('ApplicationModule', () => {
  let module: TestingModule;
  let workflowEngineService: WorkflowEngineService;
  let notificationService: NotificationService;
  let prismaService: PrismaService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [ApplicationModule],
      providers: [
        {
          provide: MailerService,
          useValue: {
            sendEmail: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    })
      .overrideProvider(PrismaService)
      .useValue({
        application: {
          findMany: jest.fn(),
          findUnique: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          count: jest.fn(),
        },
        application_step: {
          findMany: jest.fn(),
          findFirst: jest.fn(),
          create: jest.fn(),
          createMany: jest.fn(),
          update: jest.fn(),
        },
        workflow_template: {
          findFirst: jest.fn(),
          findUnique: jest.fn(),
        },
        notification_queue: {
          create: jest.fn(),
          update: jest.fn(),
          findMany: jest.fn(),
          delete: jest.fn(),
        },
        notification_template: {
          findUnique: jest.fn(),
        },
        application_document: {
          findMany: jest.fn(),
        },
      })
      .compile();

    workflowEngineService = module.get<WorkflowEngineService>(WorkflowEngineService);
    notificationService = module.get<NotificationService>(NotificationService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Service Instantiation', () => {
    it('should instantiate WorkflowEngineService', () => {
      expect(workflowEngineService).toBeDefined();
      expect(workflowEngineService).toBeInstanceOf(WorkflowEngineService);
    });

    it('should instantiate NotificationService', () => {
      expect(notificationService).toBeDefined();
      expect(notificationService).toBeInstanceOf(NotificationService);
    });

    it('should instantiate PrismaService', () => {
      expect(prismaService).toBeDefined();
    });
  });

  describe('Module Configuration', () => {
    it('should export WorkflowEngineService', () => {
      const service = module.get<WorkflowEngineService>(WorkflowEngineService);
      expect(service).toBeDefined();
    });

    it('should export NotificationService', () => {
      const service = module.get<NotificationService>(NotificationService);
      expect(service).toBeDefined();
    });

    it('should export PrismaService', () => {
      const service = module.get<PrismaService>(PrismaService);
      expect(service).toBeDefined();
    });
  });

  describe('Service Methods', () => {
    it('should have all required WorkflowEngineService methods', () => {
      const methods = [
        'initializeWorkflow',
        'advanceWorkflow',
        'validateStepCompletion',
        'checkStepDependencies',
        'checkOverdueSteps',
        'getActiveTemplate',
      ];

      methods.forEach(method => {
        expect(typeof workflowEngineService[method]).toBe('function');
      });
    });

    it('should have all required NotificationService methods', () => {
      const methods = [
        'sendNotification',
        'scheduleNotification',
        'processNotificationQueue',
        'createNotificationFromTemplate',
      ];

      methods.forEach(method => {
        expect(typeof notificationService[method]).toBe('function');
      });
    });
  });
});
