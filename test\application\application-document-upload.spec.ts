/**
 * Application Document Upload Tests
 *
 * Tests for the new document upload functionality in ApplicationService.
 * Verifies that documents can be uploaded and linked to applications correctly.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { DocumentType } from '@prisma/client';

describe('ApplicationService - Document Upload', () => {
  let service: ApplicationService;
  let prismaService: PrismaService;
  let mediaService: MediaService;
  let documentVaultService: DocumentVaultService;

  // Mock file for testing
  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-passport.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024 * 1024, // 1MB
    buffer: Buffer.from('mock pdf content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  const mockApplication = {
    id: 'app-123',
    user_id: 'user-123',
    guest_email: null,
    assigned_to: null,
  } as any;

  const mockUploadedDocument = {
    id: 'doc-123',
    document_name: 'Test Passport',
    file_path: 'documents/test-passport.pdf',
    file_size: 1024 * 1024,
    created_at: new Date(),
    original_filename: 'test-passport.pdf',
    document_type: DocumentType.Passport,
    file_hash: 'mock-hash',
    mime_type: 'application/pdf',
    tags: ['identity', 'required'],
    expiry_date: null,
    user_id: 'user-123',
    updated_at: new Date(),
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        {
          provide: PrismaService,
          useValue: {
            application: {
              findUnique: jest.fn(),
            },
            application_document: {
              findFirst: jest.fn(),
              update: jest.fn(),
              create: jest.fn(),
            },
          },
        },
        {
          provide: MediaService,
          useValue: {
            uploadFile: jest.fn(),
          },
        },
        {
          provide: DocumentVaultService,
          useValue: {
            uploadApplicationDocument: jest.fn(),
          },
        },
        {
          provide: ApplicationFormService,
          useValue: {},
        },
        {
          provide: ApplicationDocumentService,
          useValue: {},
        },
        {
          provide: ApplicationTransformerService,
          useValue: {},
        },
        {
          provide: NotificationService,
          useValue: {
            sendNotification: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);
    prismaService = module.get<PrismaService>(PrismaService);
    mediaService = module.get<MediaService>(MediaService);
    documentVaultService =
      module.get<DocumentVaultService>(DocumentVaultService);
  });

  describe('uploadApplicationDocument', () => {
    const uploadMetadata = {
      document_name: 'Test Passport',
      document_type: DocumentType.Passport,
      document_category: 'identity',
      stage_order: 1,
      required: true,
      tags: ['identity', 'required'],
      expiry_date: '2030-12-31',
    };

    it('should upload document successfully for existing application', async () => {
      // Arrange
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);
      // Mock findFirst call - no existing document record found
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(null); // No existing document record
      jest
        .spyOn(prismaService.application_document, 'create')
        .mockResolvedValue({
          id: 'app-doc-123',
          application_id: 'app-123',
          document_vault_id: 'doc-123',
          stage_order: 1,
          file_name: 'Test Passport',
          file_url: 'documents/test-passport.pdf',
          required: true,
          status: 'uploaded',
          request_reason: 'Document for stage 1',
          uploaded_by: 'user-123',
          upload_date: new Date(),
          submitted_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        } as any);

      // Act
      const result = await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        uploadMetadata,
        'user-123',
      );

      // Assert
      expect(result).toEqual({
        document_id: 'doc-123',
        document_name: 'Test Passport',
        file_path: 'documents/test-passport.pdf',
        file_size: 1024 * 1024,
        upload_date: mockUploadedDocument.created_at.toISOString(),
        status: 'uploaded',
      });

      expect(prismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'app-123' },
        select: {
          id: true,
          user_id: true,
          guest_email: true,
          assigned_to: true,
        },
      });

      // Check that we first look for existing document record
      expect(prismaService.application_document.findFirst).toHaveBeenCalledWith(
        {
          where: {
            application_id: 'app-123',
            stage_order: 1,
            file_name: 'Test Passport',
          },
        },
      );

      expect(
        documentVaultService.uploadApplicationDocument,
      ).toHaveBeenCalledWith(
        mockFile,
        'app-123',
        {
          document_name: 'Test Passport',
          document_type: 'Other', // Changed to match the application service implementation
          document_category: 'identity',
          expiry_date: new Date('2030-12-31'),
          application_id: 'app-123',
          guest_email: null,
        },
        'user-123',
      );

      expect(prismaService.application_document.create).toHaveBeenCalledWith({
        data: {
          application_id: 'app-123',
          document_vault_id: 'doc-123',
          stage_order: 1,
          file_name: 'Test Passport',
          file_url: 'documents/test-passport.pdf',
          required: true,
          status: 'uploaded',
          request_reason: 'Document for stage 1',
          uploaded_by: 'user-123',
          upload_date: expect.any(Date),
          submitted_at: expect.any(Date),
        },
      });
    });

    it('should update existing application_document record if found', async () => {
      // Arrange
      const existingDocumentRecord = {
        id: 'existing-app-doc-123',
        application_id: 'app-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'pending',
        document_vault_id: 'old-doc-123',
      } as any;

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);

      // Mock findFirst call to return existing document record
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord); // Found existing record

      jest
        .spyOn(prismaService.application_document, 'update')
        .mockResolvedValue({
          ...existingDocumentRecord,
          document_vault_id: 'doc-123',
          file_url: 'documents/test-passport.pdf',
          status: 'uploaded',
          uploaded_by: 'user-123',
          upload_date: new Date(),
          updated_at: new Date(),
        } as any);

      // Act
      const result = await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        uploadMetadata,
        'user-123',
      );

      // Assert
      expect(result.status).toBe('uploaded');

      // Check that we first look for existing document record
      expect(prismaService.application_document.findFirst).toHaveBeenCalledWith(
        {
          where: {
            application_id: 'app-123',
            stage_order: 1,
            file_name: 'Test Passport',
          },
        },
      );

      expect(prismaService.application_document.update).toHaveBeenCalledWith({
        where: { id: 'existing-app-doc-123' },
        data: {
          document_vault_id: 'doc-123',
          file_url: 'documents/test-passport.pdf',
          required: true,
          status: 'uploaded',
          uploaded_by: 'user-123',
          upload_date: expect.any(Date),
          submitted_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });

      // Ensure create was not called since we updated existing record
      expect(prismaService.application_document.create).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when application does not exist', async () => {
      // Arrange
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.uploadApplicationDocument(
          'non-existent-app',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow(NotFoundException);

      expect(prismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent-app' },
        select: {
          id: true,
          user_id: true,
          guest_email: true,
          assigned_to: true,
        },
      });
    });

    it('should handle document vault service errors', async () => {
      // Arrange
      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockRejectedValue(new BadRequestException('File type not supported'));

      // Act & Assert
      await expect(
        service.uploadApplicationDocument(
          'app-123',
          mockFile,
          uploadMetadata,
          'user-123',
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it('should create new record when no existing document found', async () => {
      // Arrange
      const metadataWithoutExpiry = { ...uploadMetadata };
      delete metadataWithoutExpiry.expiry_date;

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);
      // Mock findFirst call - no existing document record found
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(null); // No existing document record
      jest
        .spyOn(prismaService.application_document, 'create')
        .mockResolvedValue({} as any);

      // Act
      await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        metadataWithoutExpiry,
        'user-123',
      );

      // Assert
      expect(
        documentVaultService.uploadApplicationDocument,
      ).toHaveBeenCalledWith(
        mockFile,
        'app-123',
        expect.objectContaining({
          document_name: 'Test Passport',
          document_type: 'Other',
          document_category: 'identity',
          expiry_date: undefined,
          application_id: 'app-123',
          guest_email: null,
        }),
        'user-123',
      );

      expect(prismaService.application_document.create).toHaveBeenCalled();
    });

    it('should prevent duplicate records by updating existing document for same application/stage/name', async () => {
      // This test specifically validates our fix for the duplicate record issue
      // Arrange
      const existingDocumentRecord = {
        id: 'existing-doc-123',
        application_id: 'app-123',
        stage_order: 1,
        file_name: 'Test Passport',
        status: 'uploaded',
        document_vault_id: 'old-vault-123',
        file_url: 'old-documents/old-passport.pdf',
      } as any;

      jest
        .spyOn(prismaService.application, 'findUnique')
        .mockResolvedValue(mockApplication);
      jest
        .spyOn(documentVaultService, 'uploadApplicationDocument')
        .mockResolvedValue(mockUploadedDocument);

      // Mock findFirst to return existing document record (simulating duplicate scenario)
      jest
        .spyOn(prismaService.application_document, 'findFirst')
        .mockResolvedValue(existingDocumentRecord);

      jest
        .spyOn(prismaService.application_document, 'update')
        .mockResolvedValue({
          ...existingDocumentRecord,
          document_vault_id: 'doc-123', // New document vault ID
          file_url: 'documents/test-passport.pdf', // New file URL
          status: 'uploaded',
          uploaded_by: 'user-123',
          upload_date: new Date(),
          updated_at: new Date(),
        } as any);

      // Act
      const result = await service.uploadApplicationDocument(
        'app-123',
        mockFile,
        uploadMetadata,
        'user-123',
      );

      // Assert
      expect(result.status).toBe('uploaded');
      expect(result.document_id).toBe('doc-123'); // Should use new document ID
      expect(result.file_path).toBe('documents/test-passport.pdf'); // Should use new file path

      // Verify that we checked for existing record first
      expect(prismaService.application_document.findFirst).toHaveBeenCalledWith(
        {
          where: {
            application_id: 'app-123',
            stage_order: 1,
            file_name: 'Test Passport',
          },
        },
      );

      // Verify that we updated the existing record instead of creating a new one
      expect(prismaService.application_document.update).toHaveBeenCalledWith({
        where: { id: 'existing-doc-123' },
        data: {
          document_vault_id: 'doc-123',
          file_url: 'documents/test-passport.pdf',
          required: true,
          status: 'uploaded',
          uploaded_by: 'user-123',
          upload_date: expect.any(Date),
          submitted_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });

      // Most importantly: verify that create was NOT called (this prevents duplicates)
      expect(prismaService.application_document.create).not.toHaveBeenCalled();
    });
  });
});
