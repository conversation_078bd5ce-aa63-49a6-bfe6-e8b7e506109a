/**
 * Agent Integration Tests
 *
 * End-to-end tests for agent management endpoints.
 * Tests the complete flow from HTTP request to database operations.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { AgentModule } from '../../src/agent/agent.module';
import { PrismaService } from '../../src/utils/prisma.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import { AgentStatus } from '@prisma/client';

describe('Agent Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;

  // Mock data
  const mockAdmin = {
    id: 'admin_123',
    name: 'Admin User',
    email: '<EMAIL>',
  };

  const mockAgent = {
    id: 'agent_123',
    name: '<PERSON>',
    email: '<EMAIL>',
    password_hash: 'hashed_password',
    phone: '+353-1-234-5678',
    status: AgentStatus.Active,
    created_at: new Date(),
    updated_at: new Date(),
    created_by_admin_id: mockAdmin.id,
  };

  // Mock services
  const mockPrismaService = {
    agent: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
    },
    application: {
      count: jest.fn(),
    },
  };

  const mockMailerService = {
    sendEmail: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AgentModule],
    })
      .overrideProvider(PrismaService)
      .useValue(mockPrismaService)
      .overrideProvider(MailerService)
      .useValue(mockMailerService)
      .overrideProvider(LoggerService)
      .useValue(mockLoggerService)
      .compile();

    app = moduleFixture.createNestApplication();
    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /agents/register', () => {
    const createAgentDto = {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+353-1-234-5678',
    };

    it('should register agent successfully with admin token', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.agent.findUnique.mockResolvedValue(null);
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);
      mockPrismaService.agent.create.mockResolvedValue(mockAgent);
      mockMailerService.sendEmail.mockResolvedValue(undefined);

      const response = await request(app.getHttpServer())
        .post('/agents/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createAgentDto)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Agent created successfully. Welcome email sent.',
        agent: expect.objectContaining({
          name: createAgentDto.name,
          email: createAgentDto.email,
        }),
        temporaryPassword: expect.any(String),
      });
    });

    it('should return 401 without admin token', async () => {
      await request(app.getHttpServer())
        .post('/agents/register')
        .send(createAgentDto)
        .expect(401);
    });

    it('should return 409 if agent email already exists', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);

      await request(app.getHttpServer())
        .post('/agents/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createAgentDto)
        .expect(409);
    });
  });

  describe('POST /agents/login', () => {
    const loginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should login agent successfully', async () => {
      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      // Mock bcrypt.compare to return true
      jest.doMock('bcrypt', () => ({
        compare: jest.fn().mockResolvedValue(true),
      }));

      const response = await request(app.getHttpServer())
        .post('/agents/login')
        .send(loginDto)
        .expect(200);

      expect(response.body).toMatchObject({
        agent: expect.objectContaining({
          id: mockAgent.id,
          email: mockAgent.email,
        }),
        backendTokens: expect.objectContaining({
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
          expiresIn: expect.any(Number),
        }),
      });
      expect(response.body.agent).not.toHaveProperty('password_hash');
    });

    it('should return 401 for invalid credentials', async () => {
      mockPrismaService.agent.findUnique.mockResolvedValue(null);

      await request(app.getHttpServer())
        .post('/agents/login')
        .send(loginDto)
        .expect(401);
    });
  });

  describe('GET /agents', () => {
    it('should return paginated agents list for admin', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const mockAgentsWithAdmin = [
        {
          ...mockAgent,
          created_by_admin: {
            id: mockAdmin.id,
            name: mockAdmin.name,
            email: mockAdmin.email,
          },
        },
      ];

      mockPrismaService.agent.count.mockResolvedValue(1);
      mockPrismaService.agent.findMany.mockResolvedValue(mockAgentsWithAdmin);

      const response = await request(app.getHttpServer())
        .get('/agents')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toMatchObject({
        agents: expect.arrayContaining([
          expect.objectContaining({
            id: mockAgent.id,
            name: mockAgent.name,
            email: mockAgent.email,
          }),
        ]),
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should return 401 without admin token', async () => {
      await request(app.getHttpServer())
        .get('/agents')
        .expect(401);
    });
  });

  describe('GET /agents/:id', () => {
    it('should return agent details for admin', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const mockAgentWithAdmin = {
        ...mockAgent,
        created_by_admin: {
          id: mockAdmin.id,
          name: mockAdmin.name,
          email: mockAdmin.email,
        },
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgentWithAdmin);

      const response = await request(app.getHttpServer())
        .get(`/agents/${mockAgent.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: mockAgent.id,
        name: mockAgent.name,
        email: mockAgent.email,
        created_by_admin: expect.objectContaining({
          id: mockAdmin.id,
          name: mockAdmin.name,
        }),
      });
    });

    it('should allow agent to access own details', async () => {
      const agentToken = await jwtService.signAsync(
        { id: mockAgent.id, email: mockAgent.email, tokenType: 'agent' },
        { secret: process.env.jwtAgentSecretKey }
      );

      const mockAgentWithAdmin = {
        ...mockAgent,
        created_by_admin: {
          id: mockAdmin.id,
          name: mockAdmin.name,
          email: mockAdmin.email,
        },
      };

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgentWithAdmin);

      const response = await request(app.getHttpServer())
        .get(`/agents/${mockAgent.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: mockAgent.id,
        name: mockAgent.name,
        email: mockAgent.email,
      });
    });

    it('should return 403 if agent tries to access another agent', async () => {
      const agentToken = await jwtService.signAsync(
        { id: 'different_agent_id', email: '<EMAIL>', tokenType: 'agent' },
        { secret: process.env.jwtAgentSecretKey }
      );

      await request(app.getHttpServer())
        .get(`/agents/${mockAgent.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(403);
    });
  });

  describe('PUT /agents/:id', () => {
    const updateAgentDto = {
      name: 'John Smith Updated',
      email: '<EMAIL>',
    };

    it('should update agent successfully for admin', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      const updatedAgent = { ...mockAgent, ...updateAgentDto };
      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockPrismaService.agent.update.mockResolvedValue(updatedAgent);

      const response = await request(app.getHttpServer())
        .put(`/agents/${mockAgent.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateAgentDto)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Agent updated successfully',
        agent: expect.objectContaining({
          name: updateAgentDto.name,
          email: updateAgentDto.email,
        }),
      });
    });

    it('should prevent agent from updating status', async () => {
      const agentToken = await jwtService.signAsync(
        { id: mockAgent.id, email: mockAgent.email, tokenType: 'agent' },
        { secret: process.env.jwtAgentSecretKey }
      );

      const updateWithStatus = { ...updateAgentDto, status: AgentStatus.Suspended };

      await request(app.getHttpServer())
        .put(`/agents/${mockAgent.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateWithStatus)
        .expect(403);
    });
  });

  describe('DELETE /agents/:id', () => {
    it('should delete agent successfully for admin', async () => {
      const adminToken = await jwtService.signAsync(
        { id: mockAdmin.id, email: mockAdmin.email, tokenType: 'admin' },
        { secret: process.env.jwtAdminSecretKey }
      );

      mockPrismaService.agent.findUnique.mockResolvedValue(mockAgent);
      mockPrismaService.application.count.mockResolvedValue(0);
      mockPrismaService.agent.update.mockResolvedValue(undefined);

      const response = await request(app.getHttpServer())
        .delete(`/agents/${mockAgent.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Agent deleted successfully',
      });
    });

    it('should return 401 without admin token', async () => {
      await request(app.getHttpServer())
        .delete(`/agents/${mockAgent.id}`)
        .expect(401);
    });
  });
});
