model blog {
  id        String    @id @default(cuid())
  title     String
  slug      String    @unique
  summary   String
  blogger   String
  img       String
  desc      String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  comments  comment[]
}

model comment {
  id        String    @id @default(cuid())
  content   String
  blogId    String
  authorId  String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  parentId  String?
  author    user      @relation(fields: [authorId], references: [id])
  blog      blog      @relation(fields: [blogId], references: [id])
  parent    comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies   comment[] @relation("CommentReplies")
}
