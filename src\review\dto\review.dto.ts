import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsInt, <PERSON>, <PERSON> } from 'class-validator';

export class ReviewDto {
  @ApiProperty({
    description: 'The message of the review',
    example: 'Great mentoring session!',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'The rating of the mentor, between 1 and 5',
    example: 5,
  })
  @IsInt()
  @Min(1) // Minimum rating
  @Max(5) // Maximum rating
  rating: number;

  @ApiProperty({
    description: 'The ID of the mentor being reviewed',
    example: 'mentor123',
  })
  @IsString()
  mentorId: string;
}
