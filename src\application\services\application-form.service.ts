/**
 * Application Form Service
 * 
 * Handles dynamic form data management for workflow applications.
 * Provides functionality to populate, update, and retrieve form data
 * based on workflow templates.
 */

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../utils/prisma.service';

export interface FormFieldData {
  fieldName: string;
  fieldType: string;
  required: boolean;
  fieldValue?: string;
  fieldOptions?: any;
  showToClient: boolean;
}

export interface StageFormData {
  stageOrder: number;
  fields: FormFieldData[];
}

export interface FieldUpdate {
  fieldName: string;
  fieldValue: string;
}

export interface StageFieldUpdate {
  stageOrder: number;
  fields: FieldUpdate[];
}

@Injectable()
export class ApplicationFormService {
  private readonly logger = new Logger(ApplicationFormService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Populate form data from workflow template
   * Called when an application is created to initialize form fields
   */
  async populateFormDataFromTemplate(
    applicationId: string,
    workflowTemplate: any[]
  ): Promise<void> {
    try {
      this.logger.log(`Populating form data for application: ${applicationId}`);

      if (!workflowTemplate || !Array.isArray(workflowTemplate)) {
        this.logger.warn(`No valid workflow template found for application: ${applicationId}`);
        return;
      }

      const formRecords = [];

      // Process each stage in the workflow template
      for (const stage of workflowTemplate) {
        const stageOrder = stage.stageOrder || 1;
        
        // Check if stage has custom form fields
        if (stage.customForm && Array.isArray(stage.customForm)) {
          for (const field of stage.customForm) {
            formRecords.push({
              id: `af_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              application_id: applicationId,
              stage_order: stageOrder,
              field_name: field.fieldName,
              field_type: field.fieldType,
              required: field.required || false,
              field_value: null, // Start with null value
              field_options: field.options || null,
              show_to_client: field.showToClient !== false, // Default to true
            });
          }
        }
      }

      if (formRecords.length > 0) {
        // Use createMany for bulk insert
        await this.prisma.application_form.createMany({
          data: formRecords,
          skipDuplicates: true, // Skip if duplicate constraint is violated
        });

        this.logger.log(
          `Created ${formRecords.length} form fields for application: ${applicationId}`
        );
      } else {
        this.logger.log(`No form fields to create for application: ${applicationId}`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to populate form data for application: ${applicationId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Update form field values
   * Called when user submits form data updates
   */
  async updateFormFields(
    applicationId: string,
    formUpdates: StageFieldUpdate[]
  ): Promise<void> {
    try {
      this.logger.log(`Updating form fields for application: ${applicationId}`);

      // Validate application exists
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: { id: true },
      });

      if (!application) {
        throw new NotFoundException(`Application not found: ${applicationId}`);
      }

      // Process each stage update
      for (const stageUpdate of formUpdates) {
        const { stageOrder, fields } = stageUpdate;

        // Process each field update in the stage
        for (const fieldUpdate of fields) {
          const { fieldName, fieldValue } = fieldUpdate;

          // Update the specific field
          await this.prisma.application_form.updateMany({
            where: {
              application_id: applicationId,
              stage_order: stageOrder,
              field_name: fieldName,
            },
            data: {
              field_value: fieldValue,
              updated_at: new Date(),
            },
          });
        }
      }

      this.logger.log(`Successfully updated form fields for application: ${applicationId}`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to update form fields for application: ${applicationId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Get form data by application ID
   * Returns form data grouped by stage
   */
  async getFormDataByApplication(applicationId: string): Promise<StageFormData[]> {
    try {
      const formFields = await this.prisma.application_form.findMany({
        where: { application_id: applicationId },
        orderBy: [
          { stage_order: 'asc' },
          { field_name: 'asc' },
        ],
      });

      // Group fields by stage
      const stageMap = new Map<number, FormFieldData[]>();

      for (const field of formFields) {
        if (!stageMap.has(field.stage_order)) {
          stageMap.set(field.stage_order, []);
        }

        stageMap.get(field.stage_order)!.push({
          fieldName: field.field_name,
          fieldType: field.field_type,
          required: field.required,
          fieldValue: field.field_value || undefined,
          fieldOptions: field.field_options,
          showToClient: field.show_to_client,
        });
      }

      // Convert map to array
      const stageFormData: StageFormData[] = [];
      for (const [stageOrder, fields] of stageMap.entries()) {
        stageFormData.push({
          stageOrder,
          fields,
        });
      }

      return stageFormData;
    } catch (error) {
      this.logger.error(
        `Failed to get form data for application: ${applicationId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Get form fields for a specific stage
   */
  async getFormFieldsByStage(
    applicationId: string,
    stageOrder: number
  ): Promise<FormFieldData[]> {
    try {
      const formFields = await this.prisma.application_form.findMany({
        where: {
          application_id: applicationId,
          stage_order: stageOrder,
        },
        orderBy: { field_name: 'asc' },
      });

      return formFields.map(field => ({
        fieldName: field.field_name,
        fieldType: field.field_type,
        required: field.required,
        fieldValue: field.field_value || undefined,
        fieldOptions: field.field_options,
        showToClient: field.show_to_client,
      }));
    } catch (error) {
      this.logger.error(
        `Failed to get form fields for application: ${applicationId}, stage: ${stageOrder}`,
        error
      );
      throw error;
    }
  }

  /**
   * Check if application has any form data
   */
  async hasFormData(applicationId: string): Promise<boolean> {
    try {
      const count = await this.prisma.application_form.count({
        where: { application_id: applicationId },
      });
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Failed to check form data existence for application: ${applicationId}`,
        error
      );
      return false;
    }
  }

  /**
   * Delete all form data for an application
   * Used for cleanup or re-initialization
   */
  async deleteFormData(applicationId: string): Promise<void> {
    try {
      await this.prisma.application_form.deleteMany({
        where: { application_id: applicationId },
      });

      this.logger.log(`Deleted form data for application: ${applicationId}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete form data for application: ${applicationId}`,
        error
      );
      throw error;
    }
  }
}
