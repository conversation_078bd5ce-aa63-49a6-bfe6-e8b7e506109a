module.exports = {
  displayName: 'Notification Service Tests',
  testMatch: ['<rootDir>/test/notification/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/application/services/notification.service.ts',
    '!src/application/**/*.spec.ts',
    '!src/application/**/*.interface.ts',
  ],
  coverageDirectory: 'coverage/notification',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
};
