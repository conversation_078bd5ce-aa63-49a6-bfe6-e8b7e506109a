generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["prismaSchemaFolder"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

enum Day {
  Monday
  Tuesday
  Wednesday
  Thursday
  Friday
  Saturday
  Sunday
}

enum Status {
  Accepted
  Rejected
  Pending
  Completed
  Active
  Inactive
  Blocked
  Cancelled
  Refunded
}

enum Provider {
  credentials
  google
}

// New enums for Dynamic Workflow System
enum ApplicationStatus {
  Draft
  Submitted
  Under_Review
  Additional_Info_Required
  Approved
  Rejected
  Completed
  Cancelled
  On_Hold
}

enum WorkflowStepStatus {
  Not_Started
  In_Progress
  Completed
  Skipped
  Blocked
  Requires_Review
}

enum DocumentStatus {
  Pending
  Under_Review
  Approved
  Rejected
  Required_Revision
  Expired
  Request
}

enum DocumentType {
  Passport
  Resume
  Offer_Letter
  Educational_Certificate
  Work_Experience_Letter
  Medical_Report
  Police_Clearance
  Birth_Certificate
  Marriage_Certificate
  Bank_Statement
  Insurance_Document
  Other
}

enum NotificationChannel {
  Email
  In_App
  SMS
  Push
}

enum NotificationStatus {
  Pending
  Sent
  Delivered
  Failed
  Cancelled
}

enum PriorityLevel {
  Low
  Medium
  High
  Critical
}

enum AgentStatus {
  Active
  Inactive
  Suspended
}
