#!/usr/bin/env ts-node

/**
 * Test script for API endpoint structure validation
 * This script validates that the new application management endpoints are properly structured
 */

import * as fs from 'fs';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  message?: string;
  error?: string;
}

const results: TestResult[] = [];

async function runTest(
  testName: string,
  testFn: () => Promise<void>,
): Promise<void> {
  try {
    await testFn();
    results.push({ test: testName, status: 'PASS' });
    console.log(`✅ ${testName}`);
  } catch (error) {
    results.push({
      test: testName,
      status: 'FAIL',
      error: error instanceof Error ? error.message : String(error),
    });
    console.log(
      `❌ ${testName}: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

async function testControllerEndpoints(): Promise<void> {
  const controllerPath = 'src/application/abstractions/base-application.controller.ts';
  if (!fs.existsSync(controllerPath)) {
    throw new Error('Base application controller not found');
  }

  const content = fs.readFileSync(controllerPath, 'utf8');
  
  // Check for required endpoints
  const requiredEndpoints = [
    '@Get()', // Get user applications
    '@Get(\':id\')', // Get application details
    '@Patch(\':id/steps/:stepNumber\')', // Update step status
    '@Get(\'admin/all\')', // Get all applications (admin)
    '@Patch(\'admin/:id/status\')', // Update application status (admin)
  ];

  for (const endpoint of requiredEndpoints) {
    if (!content.includes(endpoint)) {
      throw new Error(`Endpoint ${endpoint} not found in base controller`);
    }
  }
}

async function testAuthenticationGuards(): Promise<void> {
  const controllerPath = 'src/application/abstractions/base-application.controller.ts';
  const content = fs.readFileSync(controllerPath, 'utf8');
  
  // Check for authentication guards
  if (!content.includes('@UseGuards(JwtGuard)')) {
    throw new Error('JWT authentication guard not found');
  }
  
  if (!content.includes('@UseGuards(JwtAdmin)')) {
    throw new Error('Admin authentication guard not found');
  }
  
  if (!content.includes('@ApiBearerAuth()')) {
    throw new Error('Bearer auth documentation not found');
  }
}

async function testSwaggerDocumentation(): Promise<void> {
  const controllerPath = 'src/application/abstractions/base-application.controller.ts';
  const content = fs.readFileSync(controllerPath, 'utf8');
  
  // Check for Swagger documentation
  const swaggerDecorators = [
    '@ApiTags(\'applications\')',
    '@ApiOperation(',
    '@ApiResponse(',
    '@ApiParam(',
    '@ApiQuery(',
  ];

  for (const decorator of swaggerDecorators) {
    if (!content.includes(decorator)) {
      throw new Error(`Swagger decorator ${decorator} not found`);
    }
  }
}

async function testDTOValidation(): Promise<void> {
  const dtoPath = 'src/application/dto/application.dto.ts';
  const content = fs.readFileSync(dtoPath, 'utf8');
  
  // Check for validation decorators
  const validationDecorators = [
    '@IsString()',
    '@IsOptional()',
    '@IsEnum(',
    '@ApiProperty(',
  ];

  for (const decorator of validationDecorators) {
    if (!content.includes(decorator)) {
      throw new Error(`Validation decorator ${decorator} not found`);
    }
  }
}

async function testErrorHandling(): Promise<void> {
  const controllerPath = 'src/application/abstractions/base-application.controller.ts';
  const content = fs.readFileSync(controllerPath, 'utf8');
  
  // Check for error handling
  if (!content.includes('HttpException')) {
    throw new Error('HttpException error handling not found');
  }
  
  if (!content.includes('try {') || !content.includes('catch (error)')) {
    throw new Error('Try-catch error handling not found');
  }
}

async function testServiceIntegration(): Promise<void> {
  const controllerPath = 'src/application/abstractions/base-application.controller.ts';
  const content = fs.readFileSync(controllerPath, 'utf8');
  
  // Check for service integration
  if (!content.includes('AbstractApplicationService')) {
    throw new Error('AbstractApplicationService integration not found');
  }
  
  if (!content.includes('this.applicationService')) {
    throw new Error('Service method calls not found');
  }
}

async function main(): Promise<void> {
  console.log('🧪 Testing API Endpoint Structure\n');

  await runTest('Controller Endpoints', testControllerEndpoints);
  await runTest('Authentication Guards', testAuthenticationGuards);
  await runTest('Swagger Documentation', testSwaggerDocumentation);
  await runTest('DTO Validation', testDTOValidation);
  await runTest('Error Handling', testErrorHandling);
  await runTest('Service Integration', testServiceIntegration);

  console.log('\n📊 Test Results Summary:');
  console.log('========================');

  const passCount = results.filter((r) => r.status === 'PASS').length;
  const failCount = results.filter((r) => r.status === 'FAIL').length;

  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(
    `📈 Success Rate: ${((passCount / results.length) * 100).toFixed(1)}%`,
  );

  if (failCount > 0) {
    console.log('\n❌ Failed Tests:');
    results
      .filter((r) => r.status === 'FAIL')
      .forEach((result) => {
        console.log(`   - ${result.test}: ${result.error}`);
      });
    
    console.log('\n🔧 API Endpoint Structure: NEEDS FIXES');
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed! API endpoint structure is correct.');
    console.log('✅ API endpoints are properly structured and documented');
  }
}

main().catch((error) => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
