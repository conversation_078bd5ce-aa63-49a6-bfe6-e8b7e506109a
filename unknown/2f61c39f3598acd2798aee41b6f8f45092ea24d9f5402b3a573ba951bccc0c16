import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { ImmigrationDto, UpdateVisibilityDto, ImmigrationQueryDto } from './dto/immigration.dto';

@Injectable()
export class ImmigrationService {
  constructor(private prisma: PrismaService) {}

  async create(dto: ImmigrationDto) {
    const packages = this.prisma.immigration_service.create({
      data: dto,
    });

    return packages;
  }
  async update(id: string, dto: ImmigrationDto) {
    const packages = this.prisma.immigration_service.update({
      where: {
        id,
      },
      data: dto,
    });

    return packages;
  }

  async remove(id: string) {
    const packages = this.prisma.immigration_service.delete({
      where: {
        id,
      },
    });

    return packages;
  }

  async getAll(filters: ImmigrationQueryDto = {}) {
    const where: any = {};

    // Handle website_visible filtering with proper logic for all cases
    if (filters.website_visible !== undefined && filters.website_visible !== null) {
      where.website_visible = filters.website_visible;
    }
    // If website_visible is not provided, return all records (no filtering)

    const packages = this.prisma.immigration_service.findMany({
      where,
      orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
    });

    return packages;
  }

  async updateVisibility(id: string, dto: UpdateVisibilityDto) {
    const packages = this.prisma.immigration_service.update({
      where: {
        id,
      },
      data: {
        website_visible: dto.website_visible,
      },
    });

    return packages;
  }
}
