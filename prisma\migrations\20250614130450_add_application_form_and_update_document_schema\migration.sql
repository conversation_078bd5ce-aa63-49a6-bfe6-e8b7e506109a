/*
  Warnings:

  - You are about to drop the column `approval_status` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `compliance_notes` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `document_requirement` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `internal_notes` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `is_custom_request` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `is_optional` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `is_required` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `meets_requirements` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `request_deadline` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `request_priority` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `review_status` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `submission_status` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the column `validation_errors` on the `application_document` table. All the data in the column will be lost.
  - You are about to drop the `application_step` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `steps` to the `application` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_name` to the `application_document` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_url` to the `application_document` table without a default value. This is not possible if the table is not empty.
  - Added the required column `stage_order` to the `application_document` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "application_document_is_custom_request_idx";

-- DropIndex
DROP INDEX "application_document_is_required_idx";

-- DropIndex
DROP INDEX "application_document_request_deadline_idx";

-- DropIndex
DROP INDEX "application_document_reviewed_at_idx";

-- DropIndex
DROP INDEX "application_document_submission_status_idx";

-- AlterTable
ALTER TABLE "application" ADD COLUMN     "steps" JSONB NOT NULL;

-- AlterTable
ALTER TABLE "application_document" DROP COLUMN "approval_status",
DROP COLUMN "compliance_notes",
DROP COLUMN "document_requirement",
DROP COLUMN "internal_notes",
DROP COLUMN "is_custom_request",
DROP COLUMN "is_optional",
DROP COLUMN "is_required",
DROP COLUMN "meets_requirements",
DROP COLUMN "request_deadline",
DROP COLUMN "request_priority",
DROP COLUMN "review_status",
DROP COLUMN "submission_status",
DROP COLUMN "validation_errors",
ADD COLUMN     "file_name" TEXT NOT NULL,
ADD COLUMN     "file_url" TEXT NOT NULL,
ADD COLUMN     "required" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "stage_order" INTEGER NOT NULL,
ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN     "upload_date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "uploaded_by" TEXT;

-- DropTable
DROP TABLE "application_step";

-- CreateTable
CREATE TABLE "application_form" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL,
    "stage_order" INTEGER NOT NULL,
    "field_name" TEXT NOT NULL,
    "field_type" TEXT NOT NULL,
    "required" BOOLEAN NOT NULL DEFAULT false,
    "field_value" TEXT,
    "field_options" JSONB,
    "show_to_client" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "application_form_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "application_form_application_id_idx" ON "application_form"("application_id");

-- CreateIndex
CREATE INDEX "application_form_stage_order_idx" ON "application_form"("stage_order");

-- CreateIndex
CREATE INDEX "application_form_field_name_idx" ON "application_form"("field_name");

-- CreateIndex
CREATE INDEX "application_form_created_at_idx" ON "application_form"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "application_form_application_id_stage_order_field_name_key" ON "application_form"("application_id", "stage_order", "field_name");

-- CreateIndex
CREATE INDEX "application_document_stage_order_idx" ON "application_document"("stage_order");

-- CreateIndex
CREATE INDEX "application_document_status_idx" ON "application_document"("status");

-- CreateIndex
CREATE INDEX "application_document_upload_date_idx" ON "application_document"("upload_date");

-- AddForeignKey
ALTER TABLE "application_form" ADD CONSTRAINT "application_form_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE;
