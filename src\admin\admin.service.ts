import {
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateUserDto, LoginDto, ProgressDto } from './dto/admin.dto';
import { compare, hash } from 'bcrypt';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';

const EXPIRE_TIME = 5 * 60 * 60 * 1000;

@Injectable()
@Injectable()
export class AdminService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async create(dto: CreateUserDto) {
    const user = await this.prisma.admin.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) throw new ConflictException('email duplicated');

    const newUser = await this.prisma.admin.create({
      data: {
        ...dto,
        password: await hash(dto.password, 10),
      },
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = newUser;
    return result;
  }

  async findByEmail(email: string) {
    return await this.prisma.admin.findUnique({
      where: {
        email: email,
      },
    });
  }
  async findById(id: string) {
    return await this.prisma.user.findUnique({
      where: {
        id: id,
      },
    });
  }

  async login(dto: LoginDto) {
    const user = await this.validateUser(dto);
    const payload = {
      id: user.id,
      email: user.email,
      tokenType: 'admin',
      sub: {
        name: user.name,
      },
    };
    return {
      user,
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtAdminSecretKey,
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtRefreshTokenKey,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }

  async validateUser(dto: LoginDto) {
    const user = await this.findByEmail(dto.email);

    if (user && (await compare(dto.password, user.password))) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      return result;
    }
    throw new UnauthorizedException('Wrong email or password');
  }

  async refreshToken(user: any) {
    const payload = {
      id: user.id,
      email: user.email,
      tokenType: 'admin',
      sub: {
        name: user.name,
      },
    };

    return {
      accessToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtAdminSecretKey,
      }),
      refreshToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtRefreshTokenKey,
      }),
      expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
    };
  }

  async progress_update_mentor_service(dto: ProgressDto) {
    const data = await this.prisma.user_mentor_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async guest_progress_update_mentor_service(dto: ProgressDto) {
    const data = await this.prisma.guest_mentor_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async progress_update_package(dto: ProgressDto) {
    const data = await this.prisma.user_package.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async guest_progress_update_package(dto: ProgressDto) {
    const data = await this.prisma.guest_package.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });

    return data;
  }
  async progress_update_immigration(dto: ProgressDto) {
    const data = await this.prisma.user_immigration_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
  async guest_progress_update_immigration(dto: ProgressDto) {
    const data = await this.prisma.guest_immigration_service.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
  async progress_update_training(dto: ProgressDto) {
    const data = await this.prisma.user_training.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
  async guest_progress_update_training(dto: ProgressDto) {
    const data = await this.prisma.guest_training.update({
      where: {
        id: dto.id,
      },
      data: {
        progress: dto.status,
      },
    });
    return data;
  }
}
