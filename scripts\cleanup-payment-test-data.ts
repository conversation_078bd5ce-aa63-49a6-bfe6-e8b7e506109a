/**
 * Payment Test Data Cleanup Script
 * 
 * This script removes test payment data from all 8 payment tables.
 * Use this to clean up after testing the payment migration.
 * 
 * ⚠️  WARNING: This will delete payment data! Use with caution.
 * 
 * Usage: npm run cleanup:payment-data
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🧹 Payment Test Data Cleanup Script');
  console.log('⚠️  WARNING: This will delete payment data from all 8 payment tables!\n');

  try {
    // Check current data counts
    const currentCounts = {
      userMentorService: await prisma.user_mentor_service.count(),
      guestMentorService: await prisma.guest_mentor_service.count(),
      userPackage: await prisma.user_package.count(),
      guestPackage: await prisma.guest_package.count(),
      userImmigrationService: await prisma.user_immigration_service.count(),
      guestImmigrationService: await prisma.guest_immigration_service.count(),
      userTraining: await prisma.user_training.count(),
      guestTraining: await prisma.guest_training.count(),
    };

    const totalRecords = Object.values(currentCounts).reduce((sum, count) => sum + count, 0);

    if (totalRecords === 0) {
      console.log('✅ No payment data found. Tables are already empty.');
      return;
    }

    console.log('📊 Current payment data:');
    Object.entries(currentCounts).forEach(([table, count]) => {
      if (count > 0) {
        console.log(`   ${table}: ${count} records`);
      }
    });
    console.log(`   Total: ${totalRecords} records\n`);

    console.log('❓ Are you sure you want to delete ALL payment data?');
    console.log('   This action cannot be undone!');
    console.log('   Press Ctrl+C to cancel, or wait 10 seconds to continue...\n');

    // Wait 10 seconds before proceeding
    await new Promise(resolve => setTimeout(resolve, 10000));

    console.log('🗑️  Starting cleanup...\n');

    // Delete data from each table
    const deletionResults = {
      userMentorService: 0,
      guestMentorService: 0,
      userPackage: 0,
      guestPackage: 0,
      userImmigrationService: 0,
      guestImmigrationService: 0,
      userTraining: 0,
      guestTraining: 0,
    };

    // User payment tables
    if (currentCounts.userMentorService > 0) {
      console.log('Deleting user_mentor_service records...');
      const result = await prisma.user_mentor_service.deleteMany({});
      deletionResults.userMentorService = result.count;
      console.log(`✅ Deleted ${result.count} user_mentor_service records`);
    }

    if (currentCounts.userPackage > 0) {
      console.log('Deleting user_package records...');
      const result = await prisma.user_package.deleteMany({});
      deletionResults.userPackage = result.count;
      console.log(`✅ Deleted ${result.count} user_package records`);
    }

    if (currentCounts.userImmigrationService > 0) {
      console.log('Deleting user_immigration_service records...');
      const result = await prisma.user_immigration_service.deleteMany({});
      deletionResults.userImmigrationService = result.count;
      console.log(`✅ Deleted ${result.count} user_immigration_service records`);
    }

    if (currentCounts.userTraining > 0) {
      console.log('Deleting user_training records...');
      const result = await prisma.user_training.deleteMany({});
      deletionResults.userTraining = result.count;
      console.log(`✅ Deleted ${result.count} user_training records`);
    }

    // Guest payment tables
    if (currentCounts.guestMentorService > 0) {
      console.log('Deleting guest_mentor_service records...');
      const result = await prisma.guest_mentor_service.deleteMany({});
      deletionResults.guestMentorService = result.count;
      console.log(`✅ Deleted ${result.count} guest_mentor_service records`);
    }

    if (currentCounts.guestPackage > 0) {
      console.log('Deleting guest_package records...');
      const result = await prisma.guest_package.deleteMany({});
      deletionResults.guestPackage = result.count;
      console.log(`✅ Deleted ${result.count} guest_package records`);
    }

    if (currentCounts.guestImmigrationService > 0) {
      console.log('Deleting guest_immigration_service records...');
      const result = await prisma.guest_immigration_service.deleteMany({});
      deletionResults.guestImmigrationService = result.count;
      console.log(`✅ Deleted ${result.count} guest_immigration_service records`);
    }

    if (currentCounts.guestTraining > 0) {
      console.log('Deleting guest_training records...');
      const result = await prisma.guest_training.deleteMany({});
      deletionResults.guestTraining = result.count;
      console.log(`✅ Deleted ${result.count} guest_training records`);
    }

    // Summary
    const totalDeleted = Object.values(deletionResults).reduce((sum, count) => sum + count, 0);

    console.log('\n🎉 Cleanup completed!');
    console.log('📈 Deletion Summary:');
    Object.entries(deletionResults).forEach(([table, count]) => {
      if (count > 0) {
        console.log(`   ${table}: ${count} records deleted`);
      }
    });
    console.log(`   Total deleted: ${totalDeleted} records`);

    // Verify tables are empty
    const finalCounts = {
      userMentorService: await prisma.user_mentor_service.count(),
      guestMentorService: await prisma.guest_mentor_service.count(),
      userPackage: await prisma.user_package.count(),
      guestPackage: await prisma.guest_package.count(),
      userImmigrationService: await prisma.user_immigration_service.count(),
      guestImmigrationService: await prisma.guest_immigration_service.count(),
      userTraining: await prisma.user_training.count(),
      guestTraining: await prisma.guest_training.count(),
    };

    const remainingRecords = Object.values(finalCounts).reduce((sum, count) => sum + count, 0);

    if (remainingRecords === 0) {
      console.log('\n✅ All payment tables are now empty.');
    } else {
      console.log('\n⚠️  Some records remain:');
      Object.entries(finalCounts).forEach(([table, count]) => {
        if (count > 0) {
          console.log(`   ${table}: ${count} records`);
        }
      });
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the script
main()
  .catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
