/**
 * Application Note DTOs
 *
 * Data transfer objects for application note management functionality.
 * Provides validation and structure for note-related operations.
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, MaxLength } from 'class-validator';

/**
 * Update Application Note Request DTO
 */
export class UpdateApplicationNoteDto {
  @ApiProperty({
    description: 'Application note content',
    example: 'Client requested expedited processing due to urgent travel requirements.',
    maxLength: 2000,
  })
  @IsString()
  @MaxLength(2000, { message: 'Note cannot exceed 2000 characters' })
  note: string;
}

/**
 * Update Application Note Response DTO
 */
export class UpdateApplicationNoteResponseDto {
  @ApiProperty({
    description: 'Operation success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Application note updated successfully',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Updated note content',
    example: 'Client requested expedited processing due to urgent travel requirements.',
  })
  note?: string;
}
