import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { TrainingService } from './training.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { TrainingDto } from './dto/training.dto';

@ApiTags('training')
@Controller('training')
export class TrainingController {
  constructor(private training: TrainingService) {}
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async create(@Body() dto: TrainingDto) {
    return await this.training.create(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/:trainingId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async update(@Param('trainingId') id: string, @Body() dto: TrainingDto) {
    return await this.training.update(id, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/:trainingId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async remove(@Param('trainingId') id: string) {
    return await this.training.remove(id);
  }

  @Get('')
  async getAll() {
    return await this.training.getAll();
  }
}
