/**
 * Update Application DTOs
 *
 * Data Transfer Objects for updating application form data and document uploads.
 * Supports row-by-row updates of form fields organized by workflow stages.
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsString,
  IsNumber,
  IsOptional,
  ValidateNested,
  IsNotEmpty,
  Min,
  IsEnum,
  IsDateString,
} from 'class-validator';

/**
 * Field Update DTO - represents a single field value update
 */
export class FieldUpdateDto {
  @ApiProperty({
    description: 'Name of the form field to update',
    example: 'name',
  })
  @IsString()
  @IsNotEmpty()
  fieldName: string;

  @ApiProperty({
    description: 'New value for the field',
    example: 'John Doe',
  })
  @IsString()
  fieldValue: string;
}

/**
 * Stage Form Data DTO - represents form updates for a specific workflow stage
 */
export class StageFormDataDto {
  @ApiProperty({
    description: 'Stage order number (1, 2, 3, etc.)',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  stageOrder: number;

  @ApiProperty({
    description: 'Array of field updates for this stage',
    type: [FieldUpdateDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FieldUpdateDto)
  fields: FieldUpdateDto[];
}

/**
 * Update Application Request DTO - main request body for updating applications
 */
export class UpdateApplicationDto {
  @ApiProperty({
    description: 'Form data updates organized by stage',
    type: [StageFormDataDto],
    example: [
      {
        stageOrder: 1,
        fields: [
          {
            fieldName: 'name',
            fieldValue: 'John Doe',
          },
          {
            fieldName: 'Gender',
            fieldValue: 'Male',
          },
          {
            fieldName: 'Date of Birth',
            fieldValue: '1990-01-15',
          },
        ],
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StageFormDataDto)
  formData: StageFormDataDto[];

  @ApiPropertyOptional({
    description: 'Update the current workflow step',
    example: '2',
  })
  @IsOptional()
  @IsString()
  currentStep?: string;
}

/**
 * Update Application Response DTO - response after successful update
 */
export class UpdateApplicationResponseDto {
  @ApiProperty({
    description: 'Whether the update was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Success or error message',
    example: 'Application updated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Updated application data',
    // Note: The actual type would be ApplicationDetailsResponse but we'll reference it dynamically
  })
  data: any; // This will be ApplicationDetailsResponse from transformer service
}

/**
 * Update Current Step DTO - for updating only the current step
 */
export class UpdateCurrentStepDto {
  @ApiProperty({
    description: 'Current workflow step to update to',
    example: '2',
  })
  @IsString()
  @IsNotEmpty()
  currentStep: string;
}

/**
 * Update Current Step Response DTO - response after updating current step
 */
export class UpdateCurrentStepResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Current step updated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Updated current step',
    example: '2',
  })
  currentStep: string;

  @ApiProperty({
    description: 'Application ID',
    example: 'cmbttqvsi0007isjctfev2d8o',
  })
  applicationId: string;
}

/**
 * Application Query Parameters DTO - for filtering and pagination
 */
export class ApplicationQueryDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by service type',
    example: 'immigration',
  })
  @IsOptional()
  @IsString()
  service_type?: string;

  @ApiPropertyOptional({
    description: 'Filter by application status',
    example: 'Draft',
    enum: [
      'Draft',
      'Submitted',
      'Under_Review',
      'Additional_Info_Required',
      'Approved',
      'Rejected',
      'Completed',
      'Cancelled',
      'On_Hold',
    ],
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: 'Filter by priority level',
    example: 'Medium',
    enum: ['Low', 'Medium', 'High', 'Critical'],
  })
  @IsOptional()
  @IsString()
  priority_level?: string;

  @ApiPropertyOptional({
    description: 'Filter by creation date from (ISO 8601)',
    example: '2025-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsString()
  created_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by creation date to (ISO 8601)',
    example: '2025-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsString()
  created_to?: string;

  @ApiPropertyOptional({
    description: 'Search in application number, name, email',
    example: 'john doe',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

/**
 * DTO for agent information in application responses
 */
export class ApplicationAgentDto {
  @ApiProperty({ description: 'Agent ID' })
  id: string;

  @ApiProperty({ description: 'Agent name' })
  name: string;

  @ApiProperty({ description: 'Agent email' })
  email: string;
}

/**
 * DTO for application list item
 */
export class ApplicationListItemDto {
  @ApiProperty({ description: 'Application ID' })
  id: string;

  @ApiProperty({ description: 'Application number' })
  application_number: string;

  @ApiProperty({ description: 'Service type' })
  service_type: string;

  @ApiProperty({ description: 'Service name' })
  service_name: string;

  @ApiProperty({ description: 'Application status' })
  status: string;

  @ApiProperty({ description: 'Priority level' })
  priority_level: string;

  @ApiProperty({ description: 'Current step' })
  current_step: string;

  @ApiProperty({ description: 'Number of workflow steps' })
  numberOfSteps: number;

  @ApiProperty({
    description: 'Array of assigned agents with full details',
    type: [ApplicationAgentDto],
    isArray: true
  })
  agent_ids: ApplicationAgentDto[];

  @ApiPropertyOptional({
    description: 'Legacy assigned agent field (for backward compatibility)',
    type: ApplicationAgentDto
  })
  assigned_agent?: ApplicationAgentDto;

  @ApiPropertyOptional({ description: 'User information' })
  user?: {
    name: string;
    email: string;
    mobile?: string;
  };

  @ApiPropertyOptional({ description: 'Guest information' })
  guest?: {
    name: string;
    email: string;
    mobile: string;
  };

  @ApiProperty({ description: 'Estimated completion date' })
  estimated_completion: string | null;

  @ApiProperty({ description: 'Creation date' })
  created_at: string;

  @ApiProperty({ description: 'Last update date' })
  updated_at: string;
}

/**
 * Application List Response DTO - for paginated list responses
 */
export class ApplicationListResponseDto {
  @ApiProperty({
    description: 'Array of application list items',
    type: [ApplicationListItemDto],
    isArray: true,
  })
  data: ApplicationListItemDto[];

  @ApiProperty({
    description: 'Pagination information',
    example: {
      total: 25,
      page: 1,
      limit: 10,
      totalPages: 3,
    },
  })
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * Application Details Response DTO - for single application details
 */
export class ApplicationDetailsResponseDto {
  @ApiProperty({
    description: 'Application ID',
    example: 'cmbttqvsi0007isjctfev2d8o',
  })
  id: string;

  @ApiProperty({
    description: 'Application number',
    example: 'IMM-2025-000001',
  })
  application_number: string;

  @ApiProperty({
    description: 'Service type',
    example: 'immigration',
  })
  service_type: string;

  @ApiProperty({
    description: 'Service name',
    example: 'Express Entry - Federal Skilled Worker',
  })
  service_name: string;

  @ApiProperty({
    description: 'Application status',
    example: 'Draft',
  })
  status: string;

  @ApiProperty({
    description: 'Priority level',
    example: 'Medium',
  })
  priority_level: string;

  @ApiProperty({
    description: 'Current workflow step',
    example: '1',
  })
  current_step: string;

  @ApiPropertyOptional({
    description: 'User information (if registered user)',
  })
  user?: {
    id: string;
    name: string;
    email: string;
  };

  @ApiPropertyOptional({
    description: 'Guest information (if guest application)',
  })
  guest?: {
    name: string;
    email: string;
    mobile: string;
  };

  @ApiProperty({
    description: 'Workflow template information',
  })
  workflow_template: {
    id: string;
    name: string;
    description: string;
  };

  @ApiProperty({
    description: 'Workflow steps with form data and documents',
    isArray: true,
  })
  steps: any[]; // ApplicationStepData[] from transformer service

  @ApiPropertyOptional({
    description: 'Estimated completion date',
    example: '2025-07-15T10:30:00.000Z',
  })
  estimated_completion?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-06-14T10:30:00.000Z',
  })
  created_at: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-06-14T10:30:00.000Z',
  })
  updated_at: string;
}

/**
 * Upload Application Document Request DTO
 */
export class UploadApplicationDocumentDto {
  @ApiProperty({
    description: 'Document ID for linking to application',
    example: 'doc_123456789',
  })
  @IsString()
  @IsNotEmpty()
  document_id: string;

  @ApiProperty({
    description: 'Document name/title',
    example: 'Passport Copy',
  })
  @IsString()
  @IsNotEmpty()
  document_name: string;

  @ApiPropertyOptional({
    description: 'Document category for organization',
    example: 'identity_documents',
  })
  @IsOptional()
  @IsString()
  document_category?: string;

  @ApiProperty({
    description: 'Workflow stage order this document belongs to',
    example: 1,
  })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  stage_order: number;

  @ApiPropertyOptional({
    description: 'Additional tags for document categorization',
    example: ['identity', 'required'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Whether this document is required for the application',
    example: true,
  })
  @IsOptional()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Document expiry date (if applicable)',
    example: '2030-12-31',
  })
  @IsOptional()
  @IsString()
  expiry_date?: string;
}

/**
 * Upload Application Document Response DTO
 */
export class UploadApplicationDocumentResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Document uploaded successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Uploaded document information',
  })
  data: {
    document_id: string;
    document_name: string;
    file_path: string;
    file_size: number;
    upload_date: string;
    status: string;
  };
}

/**
 * Update Estimated Completion Request DTO
 */
export class UpdateEstimatedCompletionDto {
  @ApiProperty({
    description: 'Estimated completion date (ISO 8601 format)',
    example: '2025-07-15T10:30:00.000Z',
  })
  @IsDateString()
  @IsNotEmpty()
  estimated_completion: string;
}

/**
 * Update Estimated Completion Response DTO
 */
export class UpdateEstimatedCompletionResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Estimated completion date updated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Updated application data with new estimated completion',
  })
  data: ApplicationDetailsResponseDto;
}
