/**
 * Immigration Controller Unit Tests
 *
 * Tests for the immigration controller endpoints including:
 * - CRUD operations for immigration services
 * - Visibility management endpoint
 * - Authentication and authorization
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ImmigrationController } from '../../src/immigration/immigration.controller';
import { ImmigrationService } from '../../src/immigration/immigration.service';
import { ImmigrationDto, UpdateVisibilityDto, ImmigrationQueryDto } from '../../src/immigration/dto/immigration.dto';

describe('ImmigrationController', () => {
  let controller: ImmigrationController;

  const mockImmigrationService = {
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getAll: jest.fn(),
    updateVisibility: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
    verifyAsync: jest.fn(),
  };

  const mockImmigrationData = {
    id: 'immigration_123',
    name: 'Work Permit Application',
    amount: 50000,
    service: ['document_review', 'application_submission', 'follow_up'],
    order: 1,
    website_visible: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ImmigrationController],
      providers: [
        {
          provide: ImmigrationService,
          useValue: mockImmigrationService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    controller = module.get<ImmigrationController>(ImmigrationController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create immigration service successfully', async () => {
      const createDto: ImmigrationDto = {
        name: 'Work Permit Application',
        amount: 50000,
        service: ['document_review', 'application_submission'],
        order: 1,
        website_visible: true,
      };

      mockImmigrationService.create.mockResolvedValue(mockImmigrationData);

      const result = await controller.create(createDto);

      expect(result).toEqual(mockImmigrationData);
      expect(mockImmigrationService.create).toHaveBeenCalledWith(createDto);
    });
  });

  describe('update', () => {
    it('should update immigration service successfully', async () => {
      const updateDto: ImmigrationDto = {
        name: 'Updated Work Permit Application',
        amount: 55000,
        service: ['document_review', 'application_submission', 'interview_prep'],
        order: 1,
        website_visible: true,
      };

      const updatedData = { ...mockImmigrationData, ...updateDto };
      mockImmigrationService.update.mockResolvedValue(updatedData);

      const result = await controller.update('immigration_123', updateDto);

      expect(result).toEqual(updatedData);
      expect(mockImmigrationService.update).toHaveBeenCalledWith('immigration_123', updateDto);
    });
  });

  describe('remove', () => {
    it('should remove immigration service successfully', async () => {
      mockImmigrationService.remove.mockResolvedValue(mockImmigrationData);

      const result = await controller.remove('immigration_123');

      expect(result).toEqual(mockImmigrationData);
      expect(mockImmigrationService.remove).toHaveBeenCalledWith('immigration_123');
    });
  });

  describe('getAll', () => {
    it('should return all immigration services when no filter is provided', async () => {
      const allServices = [
        mockImmigrationData,
        { ...mockImmigrationData, id: 'immigration_456', name: 'Student Visa', website_visible: false },
      ];

      mockImmigrationService.getAll.mockResolvedValue(allServices);

      const result = await controller.getAll({});

      expect(result).toEqual(allServices);
      expect(mockImmigrationService.getAll).toHaveBeenCalledWith({});
    });

    it('should return only visible immigration services when website_visible=true', async () => {
      const visibleServices = [
        mockImmigrationData,
        { ...mockImmigrationData, id: 'immigration_456', name: 'Student Visa' },
      ];

      mockImmigrationService.getAll.mockResolvedValue(visibleServices);

      const result = await controller.getAll({ website_visible: true });

      expect(result).toEqual(visibleServices);
      expect(mockImmigrationService.getAll).toHaveBeenCalledWith({ website_visible: true });
    });

    it('should return only hidden immigration services when website_visible=false', async () => {
      const hiddenServices = [
        { ...mockImmigrationData, id: 'immigration_789', name: 'Hidden Service', website_visible: false },
      ];

      mockImmigrationService.getAll.mockResolvedValue(hiddenServices);

      const result = await controller.getAll({ website_visible: false });

      expect(result).toEqual(hiddenServices);
      expect(mockImmigrationService.getAll).toHaveBeenCalledWith({ website_visible: false });
    });

    it('should return empty array when no services match the filter', async () => {
      mockImmigrationService.getAll.mockResolvedValue([]);

      const result = await controller.getAll({ website_visible: true });

      expect(result).toEqual([]);
      expect(mockImmigrationService.getAll).toHaveBeenCalledWith({ website_visible: true });
    });
  });

  describe('updateVisibility', () => {
    it('should update visibility to false successfully', async () => {
      const visibilityDto: UpdateVisibilityDto = {
        website_visible: false,
      };

      const updatedData = { ...mockImmigrationData, website_visible: false };
      mockImmigrationService.updateVisibility.mockResolvedValue(updatedData);

      const result = await controller.updateVisibility('immigration_123', visibilityDto);

      expect(result).toEqual(updatedData);
      expect(mockImmigrationService.updateVisibility).toHaveBeenCalledWith('immigration_123', visibilityDto);
    });

    it('should update visibility to true successfully', async () => {
      const visibilityDto: UpdateVisibilityDto = {
        website_visible: true,
      };

      const updatedData = { ...mockImmigrationData, website_visible: true };
      mockImmigrationService.updateVisibility.mockResolvedValue(updatedData);

      const result = await controller.updateVisibility('immigration_123', visibilityDto);

      expect(result).toEqual(updatedData);
      expect(mockImmigrationService.updateVisibility).toHaveBeenCalledWith('immigration_123', visibilityDto);
    });
  });
});
