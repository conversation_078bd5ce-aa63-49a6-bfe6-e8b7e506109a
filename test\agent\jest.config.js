/**
 * Jest Configuration for Agent Tests
 *
 * Specific configuration for agent management system tests.
 * Includes proper setup for database mocking and environment variables.
 */

module.exports = {
  displayName: 'Agent Management Tests',
  testMatch: ['<rootDir>/test/agent/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/agent/**/*.(t|j)s',
    'src/guards/jwt.agent.guard.(t|j)s',
    'src/guards/jwt.admin-or-agent.guard.(t|j)s',
    '!src/agent/**/*.spec.ts',
    '!src/agent/**/*.interface.ts',
    '!src/agent/**/*.dto.ts',
  ],
  coverageDirectory: 'coverage/agent',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  testTimeout: 30000,
  verbose: true,
  
  // Environment variables for testing
  setupFiles: ['<rootDir>/test/agent/test-env.js'],
  
  // Module path mapping
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^test/(.*)$': '<rootDir>/test/$1',
  },
  
  // Global setup and teardown
  globalSetup: '<rootDir>/test/config/global-setup.ts',
  globalTeardown: '<rootDir>/test/config/global-teardown.ts',
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    'src/agent/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },
};
