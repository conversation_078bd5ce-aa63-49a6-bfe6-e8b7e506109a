import { Module } from '@nestjs/common';
import { CustomerReviewController } from './customer-review.controller';
import { CustomerReviewService } from './customer-review.service';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';

@Module({
  controllers: [CustomerReviewController],
  providers: [CustomerReviewService, PrismaService, JwtService],
})
export class CustomerReviewModule {}
