/**
 * User DTO Validation Tests
 *
 * Simple validation tests for user DTOs to verify mobile number validation
 * and optional field handling without complex dependencies.
 */

import { validate } from 'class-validator';
import { CreateUserDto, UpdateUserDto } from '../../src/user/dto/user.dto';

describe('User DTO Validation', () => {
  describe('CreateUserDto', () => {
    it('should validate successfully with all fields', async () => {
      const dto = new CreateUserDto();
      dto.name = '<PERSON>';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.mobileNo = '+353123456789';
      dto.emailVerified = false;
      dto.image = 'profile.jpg';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with only required fields', async () => {
      const dto = new CreateUserDto();
      dto.name = '<PERSON>';
      dto.email = '<EMAIL>';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with mobile number but no password', async () => {
      const dto = new CreateUserDto();
      dto.name = '<PERSON> Doe';
      dto.email = '<EMAIL>';
      dto.mobile_no = '+353123456789';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with password but no mobile number', async () => {
      const dto = new CreateUserDto();
      dto.name = 'John Doe';
      dto.email = '<EMAIL>';
      dto.password = 'password123';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation with invalid email', async () => {
      const dto = new CreateUserDto();
      dto.name = 'John Doe';
      dto.email = 'invalid-email';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should fail validation with missing name', async () => {
      const dto = new CreateUserDto();
      dto.email = '<EMAIL>';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should fail validation with invalid mobile number format', async () => {
      const dto = new CreateUserDto();
      dto.name = 'John Doe';
      dto.email = '<EMAIL>';
      dto.mobile_no = 'invalid-phone';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const mobileError = errors.find(
        (error) => error.property === 'mobile_no',
      );
      expect(mobileError).toBeDefined();
      expect(mobileError.constraints.matches).toContain(
        'Mobile number must be a valid phone number format',
      );
    });

    it('should validate valid mobile number formats', async () => {
      const validNumbers = [
        '+353123456789',
        '+1234567890',
        '1234567890',
        '+441234567890',
      ];

      for (const mobile_no of validNumbers) {
        const dto = new CreateUserDto();
        dto.name = 'John Doe';
        dto.email = '<EMAIL>';
        dto.mobile_no = mobile_no;

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should reject invalid mobile number formats', async () => {
      const invalidNumbers = [
        'abc123',
        '+',
        '0123456789', // starts with 0
        '+0123456789', // starts with +0
        '++123456789', // double plus
        '************-extra', // too long with extra characters
      ];

      for (const mobile_no of invalidNumbers) {
        const dto = new CreateUserDto();
        dto.name = 'John Doe';
        dto.email = '<EMAIL>';
        dto.mobile_no = mobile_no;

        const errors = await validate(dto);
        expect(errors.length).toBeGreaterThan(0);
        const mobileError = errors.find(
          (error) => error.property === 'mobile_no',
        );
        expect(mobileError).toBeDefined();
      }
    });
  });

  describe('UpdateUserDto', () => {
    it('should validate successfully with mobile number update', async () => {
      const dto = new UpdateUserDto();
      dto.mobile_no = '+353123456789';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with all optional fields', async () => {
      const dto = new UpdateUserDto();
      dto.name = 'John Updated';
      dto.email = '<EMAIL>';
      dto.password = 'newpassword123';
      dto.image = 'new-profile.jpg';
      dto.mobile_no = '+353987654321';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate successfully with empty DTO (all fields optional)', async () => {
      const dto = new UpdateUserDto();

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation with invalid mobile number in update', async () => {
      const dto = new UpdateUserDto();
      dto.mobile_no = 'invalid-phone';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const mobileError = errors.find(
        (error) => error.property === 'mobile_no',
      );
      expect(mobileError).toBeDefined();
      expect(mobileError.constraints.matches).toContain(
        'Mobile number must be a valid phone number format',
      );
    });

    it('should fail validation with invalid email in update', async () => {
      const dto = new UpdateUserDto();
      dto.email = 'invalid-email';

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const emailError = errors.find((error) => error.property === 'email');
      expect(emailError).toBeDefined();
    });
  });

  describe('Mobile Number Regex Pattern', () => {
    const mobileRegex = /^[\+]?[1-9][\d]{0,14}$/;

    it('should match valid international formats', () => {
      const validNumbers = [
        '+353123456789',
        '+1234567890',
        '1234567890',
        '+441234567890',
        '+86123456789',
        '9876543210',
        '+12345',
        '12345678901234', // 14 digits
      ];

      validNumbers.forEach((number) => {
        expect(number).toMatch(mobileRegex);
      });
    });

    it('should reject invalid formats', () => {
      const invalidNumbers = [
        'abc123',
        '+',
        '0123456789', // starts with 0
        '+0123456789', // starts with +0
        '++123456789', // double plus
        '1234567890123456', // 16 digits (too long)
        '+1234567890123456', // too long with +
        '', // empty
        '+abc123', // letters after +
        '123abc456', // letters in middle
      ];

      invalidNumbers.forEach((number) => {
        expect(number).not.toMatch(mobileRegex);
      });
    });
  });
});
