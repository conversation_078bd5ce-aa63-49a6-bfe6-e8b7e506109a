import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsInt, IsString, Min, IsNotEmpty, IsOptional, IsBoolean, IsIn, ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';

export class ImmigrationDto {
  @ApiProperty({ description: 'Name of the package' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Amount of the package in integer format' })
  @IsInt()
  @Min(0)
  amount: number;

  @ApiProperty({ description: 'Training order' })
  @IsOptional()
  @IsInt()
  @Min(0)
  order: number;

  @ApiProperty({
    description: 'List of services included in the package',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  service: string[];

  @ApiProperty({
    description: 'Controls whether this immigration service is visible on the website',
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  website_visible?: boolean;
}

export class UpdateVisibilityDto {
  @ApiProperty({
    description: 'Controls whether this immigration service is visible on the website',
    example: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  website_visible: boolean;
}

export class ImmigrationQueryDto {
  @ApiProperty({
    description: 'Filter by website visibility. When provided, returns only services matching the visibility status. When omitted, returns all services.',
    required: false,
    example: 'true',
    enum: ['true', 'false'],
  })
  @IsOptional()
  @ValidateIf((o, value) => value !== undefined)
  @IsIn(['true', 'false', true, false], { message: 'website_visible must be either "true" or "false"' })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === true || value === false) return value;
    return value; // Return as-is for validation to catch invalid values
  })
  website_visible?: boolean;
}
