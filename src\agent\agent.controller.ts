import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { JwtAgent } from 'src/guards/jwt.agent.guard';
import { JwtAdminOrAgent } from 'src/guards/jwt.admin-or-agent.guard';
import { RefreshJwtGuard } from 'src/guards/refresh.guard';
import { GetUser } from 'src/decorator/user.decorator';
import { AgentService } from './agent.service';
import {
  CreateAgentDto,
  AgentLoginDto,
  UpdateAgentPasswordDto,
  ResetAgentPasswordDto,
  ConfirmResetAgentPasswordDto,
  UpdateAgentDto,
  AgentQueryDto,
  AgentResponseDto,
  AgentListResponseDto,
  AgentAuthResponseDto,
  AgentCreationResponseDto,
} from './dto/agent.dto';
import { LoggerService } from 'src/utils/logger.service';

@ApiTags('agents')
@Controller('agents')
export class AgentController {
  constructor(
    private agentService: AgentService,
    private logger: LoggerService,
  ) {}

  /**
   * Register new agent (admin-only)
   */
  @Post('register')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Register new agent (Admin only)',
    description:
      'Create a new agent account with auto-generated password. Sends welcome email.',
  })
  @ApiBody({ type: CreateAgentDto })
  @ApiResponse({
    status: 201,
    description: 'Agent created successfully',
    type: AgentCreationResponseDto,
  })
  @ApiResponse({ status: 409, description: 'Agent with email already exists' })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async register(
    @Body() dto: CreateAgentDto,
    @GetUser() admin: any,
  ): Promise<AgentCreationResponseDto> {
    try {
      const result = await this.agentService.register(dto, admin.id);

      return {
        success: true,
        message: 'Agent created successfully. Welcome email sent.',
        agent: {
          id: result.agent.id,
          name: result.agent.name,
          email: result.agent.email,
          phone: result.agent.phone,
          status: result.agent.status,
          created_at: result.agent.created_at,
          updated_at: result.agent.updated_at,
        },
        temporaryPassword: result.temporaryPassword,
      };
    } catch (error) {
      this.logger.error(
        `Agent registration failed: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to register agent',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Agent login
   */
  @Post('login')
  @ApiOperation({
    summary: 'Agent login',
    description: 'Authenticate agent and return JWT tokens',
  })
  @ApiBody({ type: AgentLoginDto })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AgentAuthResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials or inactive account',
  })
  async login(@Body() dto: AgentLoginDto): Promise<AgentAuthResponseDto> {
    try {
      const result = await this.agentService.login(dto);
      return result;
    } catch (error) {
      this.logger.error(`Agent login failed: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Login failed',
        error.status || HttpStatus.UNAUTHORIZED,
      );
    }
  }

  /**
   * Refresh agent token
   */
  @Post('refresh')
  @UseGuards(RefreshJwtGuard)
  @ApiOperation({
    summary: 'Refresh agent token',
    description: 'Get new access token using refresh token',
  })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refreshToken(@Request() req) {
    try {
      return await this.agentService.refreshToken(req.user);
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`, error.stack);
      throw new HttpException('Token refresh failed', HttpStatus.UNAUTHORIZED);
    }
  }

  /**
   * Update agent password (agent self-update)
   */
  @Put(':id/password')
  @UseGuards(JwtAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update agent password',
    description: 'Agent can update their own password',
  })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ type: UpdateAgentPasswordDto })
  @ApiResponse({ status: 200, description: 'Password updated successfully' })
  @ApiResponse({ status: 401, description: 'Current password incorrect' })
  @ApiResponse({ status: 403, description: 'Can only update own password' })
  async updatePassword(
    @Param('id') id: string,
    @Body() dto: UpdateAgentPasswordDto,
    @GetUser() agent: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Ensure agent can only update their own password
      if (agent.id !== id) {
        throw new HttpException(
          'You can only update your own password',
          HttpStatus.FORBIDDEN,
        );
      }

      await this.agentService.updatePassword(id, dto);

      return {
        success: true,
        message: 'Password updated successfully',
      };
    } catch (error) {
      this.logger.error(
        `Password update failed: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to update password',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Reset agent password (without login)
   */
  @Post('reset-password')
  @ApiOperation({
    summary: 'Request password reset',
    description: 'Send password reset email to agent',
  })
  @ApiBody({ type: ResetAgentPasswordDto })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  async resetPassword(
    @Body() dto: ResetAgentPasswordDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.agentService.resetPassword(dto);

      return {
        success: true,
        message:
          'If an agent account with this email exists, a password reset link has been sent.',
      };
    } catch (error) {
      this.logger.error(`Password reset failed: ${error.message}`, error.stack);
      // Don't reveal specific error details for security
      return {
        success: true,
        message:
          'If an agent account with this email exists, a password reset link has been sent.',
      };
    }
  }

  /**
   * Confirm password reset with token
   */
  @Post('reset-password/confirm')
  @ApiOperation({
    summary: 'Confirm password reset',
    description: 'Reset password using token from email',
  })
  @ApiBody({ type: ConfirmResetAgentPasswordDto })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 401, description: 'Invalid or expired token' })
  async confirmResetPassword(
    @Body() dto: ConfirmResetAgentPasswordDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.agentService.confirmResetPassword(dto);

      return {
        success: true,
        message: 'Password reset successfully',
      };
    } catch (error) {
      this.logger.error(
        `Password reset confirmation failed: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to reset password',
        error.status || HttpStatus.UNAUTHORIZED,
      );
    }
  }

  /**
   * Get all agents (admin-only)
   */
  @Get()
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all agents (Admin only)',
    description: 'Retrieve all agents with filtering and pagination',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['Active', 'Inactive', 'Suspended'],
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by name or email',
  })
  @ApiResponse({
    status: 200,
    description: 'Agents retrieved successfully',
    type: AgentListResponseDto,
  })
  async getAllAgents(
    @Query() query: AgentQueryDto,
  ): Promise<AgentListResponseDto> {
    try {
      return await this.agentService.findAll(query);
    } catch (error) {
      this.logger.error(`Failed to get agents: ${error.message}`, error.stack);
      throw new HttpException(
        'Failed to retrieve agents',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get agent by ID
   */
  @Get(':id')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get agent by ID',
    description: 'Admin can get any agent, agent can get their own details',
  })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({
    status: 200,
    description: 'Agent retrieved successfully',
    type: AgentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  async getAgentById(
    @Param('id') id: string,
    @GetUser() user: any,
  ): Promise<AgentResponseDto> {
    try {
      // Check access permissions
      if (user.tokenType === 'agent' && user.id !== id) {
        throw new HttpException(
          'You can only access your own details',
          HttpStatus.FORBIDDEN,
        );
      }

      const agent = await this.agentService.findByIdWithAdmin(id);
      if (!agent) {
        throw new HttpException('Agent not found', HttpStatus.NOT_FOUND);
      }

      return agent;
    } catch (error) {
      this.logger.error(`Failed to get agent: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to retrieve agent',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update agent details
   */
  @Put(':id')
  @UseGuards(JwtAdminOrAgent)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update agent details',
    description:
      'Admin can update any agent, agent can update limited own fields',
  })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ type: UpdateAgentDto })
  @ApiResponse({
    status: 200,
    description: 'Agent updated successfully',
    type: AgentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  async updateAgent(
    @Param('id') id: string,
    @Body() dto: UpdateAgentDto,
    @GetUser() user: any,
  ): Promise<{ success: boolean; message: string; agent: any }> {
    try {
      // Check access permissions and filter allowed fields
      if (user.tokenType === 'agent') {
        if (user.id !== id) {
          throw new HttpException(
            'You can only update your own details',
            HttpStatus.FORBIDDEN,
          );
        }

        // Agents can only update limited fields (not status)
        if (dto.status) {
          throw new HttpException(
            'You cannot update your own status',
            HttpStatus.FORBIDDEN,
          );
        }
      }

      const updatedAgent = await this.agentService.update(id, dto, user.id);

      return {
        success: true,
        message: 'Agent updated successfully',
        agent: {
          id: updatedAgent.id,
          name: updatedAgent.name,
          email: updatedAgent.email,
          phone: updatedAgent.phone,
          status: updatedAgent.status,
          created_at: updatedAgent.created_at,
          updated_at: updatedAgent.updated_at,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to update agent: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to update agent',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete agent (admin-only)
   */
  @Delete(':id')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete agent (Admin only)',
    description: 'Soft delete agent by setting status to Inactive',
  })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete agent with active assignments',
  })
  async deleteAgent(
    @Param('id') id: string,
    @GetUser() admin: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.agentService.delete(id, admin.id);

      return {
        success: true,
        message: 'Agent deleted successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to delete agent: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to delete agent',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
