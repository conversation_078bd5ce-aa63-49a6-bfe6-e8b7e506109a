/**
 * Admin Notification Controller Tests
 * Task 8: API Implementation and Documentation
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus, HttpException } from '@nestjs/common';
import { AdminNotificationController } from '../../src/application/controllers/admin-notification.controller';
import { NotificationService } from '../../src/application/services/notification.service';
import { SendNotificationDto, NotificationType } from '../../src/application/dto/notification.dto';
import { IJWTPayload } from '../../src/types/auth';

describe('AdminNotificationController', () => {
  let controller: AdminNotificationController;
  let notificationService: jest.Mocked<NotificationService>;

  const mockAdmin: IJWTPayload = {
    id: 'admin-123',
    email: '<EMAIL>',
    sub: { name: 'Admin User' },
    iat: Date.now(),
    exp: Date.now() + 3600000,
    tokenType: 'admin',
  };

  const mockNotification = {
    id: 'notif-123',
    notification_type: 'Email',
    recipient_email: '<EMAIL>',
    subject: 'Test Notification',
    status: 'Sent',
    sent_at: new Date(),
    created_at: new Date(),
  };

  beforeEach(async () => {
    const mockNotificationService = {
      sendNotification: jest.fn(),
      scheduleNotification: jest.fn(),
      getNotificationStats: jest.fn(),
      createNotificationFromTemplate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminNotificationController],
      providers: [
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    controller = module.get<AdminNotificationController>(AdminNotificationController);
    notificationService = module.get(NotificationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('sendNotification', () => {
    it('should send notification successfully', async () => {
      const dto: SendNotificationDto = {
        notification_type: NotificationType.EMAIL,
        recipient_email: '<EMAIL>',
        subject: 'Test Notification',
        message_body: '<p>This is a test notification</p>',
      };

      notificationService.sendNotification.mockResolvedValue(mockNotification as any);

      const result = await controller.sendNotification(dto);

      expect(result).toEqual({
        status: 'success',
        message: 'Notification sent successfully',
        data: {
          id: mockNotification.id,
          notification_type: mockNotification.notification_type,
          recipient_email: mockNotification.recipient_email,
          subject: mockNotification.subject,
          status: mockNotification.status,
          sent_at: mockNotification.sent_at,
          created_at: mockNotification.created_at,
        },
      });

      expect(notificationService.sendNotification).toHaveBeenCalledWith({
        notification_type: dto.notification_type,
        template_id: dto.template_id,
        recipient_user_id: dto.recipient_user_id,
        recipient_email: dto.recipient_email,
        recipient_mobile: dto.recipient_mobile,
        subject: dto.subject,
        message_body: dto.message_body,
        application_id: dto.application_id,
        document_id: dto.document_id,
        metadata: dto.metadata,
      });
    });

    it('should handle notification service errors', async () => {
      const dto: SendNotificationDto = {
        notification_type: NotificationType.EMAIL,
        recipient_email: '<EMAIL>',
        subject: 'Test Notification',
        message_body: '<p>This is a test notification</p>',
      };

      notificationService.sendNotification.mockRejectedValue(new Error('Email service unavailable'));

      await expect(controller.sendNotification(dto)).rejects.toThrow(HttpException);
    });

    it('should validate email format', async () => {
      const dto: SendNotificationDto = {
        notification_type: NotificationType.EMAIL,
        recipient_email: 'invalid-email',
        subject: 'Test Notification',
        message_body: '<p>This is a test notification</p>',
      };

      // This would be caught by class-validator in real scenario
      // Here we simulate the validation error
      await expect(controller.sendNotification(dto)).rejects.toThrow();
    });
  });

  describe('scheduleNotification', () => {
    it('should schedule notification successfully', async () => {
      const dto = {
        notification_type: NotificationType.EMAIL,
        recipient_email: '<EMAIL>',
        subject: 'Scheduled Notification',
        message_body: '<p>This is a scheduled notification</p>',
        scheduled_at: new Date(Date.now() + 60000).toISOString(), // 1 minute from now
      };

      const scheduledNotification = {
        ...mockNotification,
        status: 'Pending',
        scheduled_at: new Date(dto.scheduled_at),
        sent_at: null,
      };

      notificationService.scheduleNotification.mockResolvedValue(scheduledNotification as any);

      const result = await controller.scheduleNotification(dto);

      expect(result).toEqual({
        status: 'success',
        message: 'Notification scheduled successfully',
        data: {
          id: scheduledNotification.id,
          notification_type: scheduledNotification.notification_type,
          recipient_email: scheduledNotification.recipient_email,
          subject: scheduledNotification.subject,
          status: scheduledNotification.status,
          scheduled_at: scheduledNotification.scheduled_at,
          created_at: scheduledNotification.created_at,
        },
      });
    });

    it('should reject past scheduled times', async () => {
      const dto = {
        notification_type: NotificationType.EMAIL,
        recipient_email: '<EMAIL>',
        subject: 'Scheduled Notification',
        message_body: '<p>This is a scheduled notification</p>',
        scheduled_at: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
      };

      await expect(controller.scheduleNotification(dto)).rejects.toThrow(HttpException);
    });
  });

  describe('getNotificationStats', () => {
    it('should return notification statistics', async () => {
      const mockStats = {
        Sent: 150,
        Pending: 25,
        Failed: 5,
      };

      notificationService.getNotificationStats.mockResolvedValue(mockStats);

      const result = await controller.getNotificationStats();

      expect(result).toEqual({
        status: 'success',
        message: 'Notification statistics retrieved successfully',
        data: mockStats,
      });

      expect(notificationService.getNotificationStats).toHaveBeenCalledWith(undefined);
    });

    it('should filter stats by application ID', async () => {
      const applicationId = 'app-123';
      const mockStats = {
        Sent: 10,
        Pending: 2,
        Failed: 1,
      };

      notificationService.getNotificationStats.mockResolvedValue(mockStats);

      const result = await controller.getNotificationStats(applicationId);

      expect(result.data).toEqual(mockStats);
      expect(notificationService.getNotificationStats).toHaveBeenCalledWith(applicationId);
    });
  });

  describe('sendNotificationFromTemplate', () => {
    it('should send notification from template successfully', async () => {
      const dto = {
        template_id: 'template-123',
        context: {
          name: 'John Doe',
          application_number: 'APP-2024-001',
          status: 'Approved',
        },
      };

      notificationService.createNotificationFromTemplate.mockResolvedValue(mockNotification as any);

      const result = await controller.sendNotificationFromTemplate(dto);

      expect(result).toEqual({
        status: 'success',
        message: 'Notification sent from template successfully',
        data: {
          id: mockNotification.id,
          notification_type: mockNotification.notification_type,
          recipient_email: mockNotification.recipient_email,
          subject: mockNotification.subject,
          status: mockNotification.status,
          sent_at: mockNotification.sent_at,
          created_at: mockNotification.created_at,
        },
      });

      expect(notificationService.createNotificationFromTemplate).toHaveBeenCalledWith(
        dto.template_id,
        dto.context,
      );
    });

    it('should handle template not found error', async () => {
      const dto = {
        template_id: 'non-existent-template',
        context: {},
      };

      notificationService.createNotificationFromTemplate.mockRejectedValue(
        new Error('Notification template not found'),
      );

      await expect(controller.sendNotificationFromTemplate(dto)).rejects.toThrow(HttpException);
    });
  });

  describe('SMS notifications', () => {
    it('should send SMS notification', async () => {
      const dto: SendNotificationDto = {
        notification_type: NotificationType.SMS,
        recipient_email: '<EMAIL>',
        recipient_mobile: '+353123456789',
        subject: 'SMS Notification',
        message_body: 'This is a test SMS notification',
      };

      const smsNotification = {
        ...mockNotification,
        notification_type: 'SMS',
        recipient_mobile: '+353123456789',
      };

      notificationService.sendNotification.mockResolvedValue(smsNotification as any);

      const result = await controller.sendNotification(dto);

      expect(result.status).toBe('success');
      expect(notificationService.sendNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          notification_type: NotificationType.SMS,
          recipient_mobile: '+353123456789',
        }),
      );
    });
  });
});
