/**
 * Notification DTOs
 * Task 8: API Implementation and Documentation
 * 
 * Data Transfer Objects for notification management
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsEnum,
  IsObject,
  IsDateString,
} from 'class-validator';

export enum NotificationType {
  EMAIL = 'Email',
  SMS = 'SMS',
  IN_APP = 'In_App',
}

export enum NotificationStatus {
  PENDING = 'Pending',
  SENT = 'Sent',
  FAILED = 'Failed',
  CANCELLED = 'Cancelled',
}

/**
 * DTO for sending notifications
 */
export class SendNotificationDto {
  @ApiProperty({
    description: 'Type of notification',
    enum: NotificationType,
    default: NotificationType.EMAIL,
  })
  @IsEnum(NotificationType)
  notification_type: NotificationType;

  @ApiPropertyOptional({
    description: 'Template ID for predefined templates',
  })
  @IsOptional()
  @IsString()
  template_id?: string;

  @ApiPropertyOptional({
    description: 'Recipient user ID (for registered users)',
  })
  @IsOptional()
  @IsString()
  recipient_user_id?: string;

  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  recipient_email: string;

  @ApiPropertyOptional({
    description: 'Recipient mobile number (for SMS)',
    example: '+353123456789',
  })
  @IsOptional()
  @IsString()
  recipient_mobile?: string;

  @ApiProperty({
    description: 'Notification subject',
    example: 'Application Status Update',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Notification message body (HTML for email, plain text for SMS)',
    example: '<p>Your application has been updated.</p>',
  })
  @IsString()
  message_body: string;

  @ApiPropertyOptional({
    description: 'Related application ID',
  })
  @IsOptional()
  @IsString()
  application_id?: string;

  @ApiPropertyOptional({
    description: 'Related document ID',
  })
  @IsOptional()
  @IsString()
  document_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { priority: 'high', category: 'status_update' },
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO for scheduling notifications
 */
export class ScheduleNotificationDto extends SendNotificationDto {
  @ApiProperty({
    description: 'Scheduled delivery time (ISO 8601 format)',
    example: '2024-01-15T10:00:00Z',
  })
  @IsDateString()
  scheduled_at: string;
}

/**
 * DTO for notification template context
 */
export class NotificationTemplateContextDto {
  @ApiProperty({
    description: 'Template ID',
  })
  @IsString()
  template_id: string;

  @ApiProperty({
    description: 'Context variables for template replacement',
    example: {
      name: 'John Doe',
      application_number: 'APP-2024-001',
      status: 'Approved',
    },
  })
  @IsObject()
  context: any;
}

/**
 * DTO for notification response
 */
export class NotificationResponseDto {
  @ApiProperty({
    description: 'Response status',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'Response message',
    example: 'Notification sent successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Notification data',
  })
  data: {
    id: string;
    notification_type: string;
    recipient_email: string;
    subject: string;
    status: string;
    sent_at?: Date;
    scheduled_at?: Date;
    created_at: Date;
  };
}

/**
 * DTO for notification filters
 */
export class NotificationFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by notification type',
    enum: NotificationType,
  })
  @IsOptional()
  @IsEnum(NotificationType)
  notification_type?: NotificationType;

  @ApiPropertyOptional({
    description: 'Filter by status',
    enum: NotificationStatus,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiPropertyOptional({
    description: 'Filter by application ID',
  })
  @IsOptional()
  @IsString()
  application_id?: string;

  @ApiPropertyOptional({
    description: 'Filter by recipient email',
  })
  @IsOptional()
  @IsEmail()
  recipient_email?: string;

  @ApiPropertyOptional({
    description: 'Filter from date (ISO 8601)',
  })
  @IsOptional()
  @IsDateString()
  date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter to date (ISO 8601)',
  })
  @IsOptional()
  @IsDateString()
  date_to?: string;

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
  })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 20,
  })
  @IsOptional()
  limit?: number;
}

/**
 * DTO for bulk notification sending
 */
export class BulkNotificationDto {
  @ApiProperty({
    description: 'List of recipient emails',
    type: [String],
    example: ['<EMAIL>', '<EMAIL>'],
  })
  @IsEmail({}, { each: true })
  recipient_emails: string[];

  @ApiProperty({
    description: 'Notification subject',
    example: 'Important Update',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Notification message body',
    example: '<p>This is an important update for all users.</p>',
  })
  @IsString()
  message_body: string;

  @ApiPropertyOptional({
    description: 'Template ID for bulk notifications',
  })
  @IsOptional()
  @IsString()
  template_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
