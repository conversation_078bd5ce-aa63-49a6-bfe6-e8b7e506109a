import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ServicesService } from './services.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ServiceDto } from './dto/services.dto';

@ApiTags('services')
@Controller('services')
export class ServicesController {
  constructor(private services: ServicesService) {}

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('/admin/:mentorId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminServiceCreate(
    @Param('mentorId') mentorId: string,
    @Body() dto: ServiceDto,
  ) {
    return await this.services.adminServiceCreate(mentorId, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/admin/:serviceId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminServiceUpdate(
    @Param('serviceId') id: string,
    @Body() dto: ServiceDto,
  ) {
    return await this.services.adminServiceUpdate(id, dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/admin/:serviceId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async adminServiceDelete(@Param('serviceId') id: string) {
    return await this.services.adminServiceDelete(id);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/:mentorId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async getServicesForAdmin(@Param('mentorId') id: string) {
    return await this.services.getServicesForAdmin(id);
  }
}
