/**
 * Workflow Master Migration Script
 *
 * This script creates the workflow_master table and its indexes.
 * It follows the same pattern as the document_master migration.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateWorkflowMaster() {
  try {
    console.log('🚀 Starting Workflow Master migration...');

    // Check if workflow_master table already exists
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'workflow_master'
      );
    `;

    if ((tableExists as any)[0]?.exists) {
      console.log('✅ workflow_master table already exists, skipping creation');
    } else {
      console.log('📝 Creating workflow_master table...');

      // Create the workflow_master table
      await prisma.$executeRaw`
        CREATE TABLE workflow_master (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          name TEXT NOT NULL,
          description TEXT,
          is_active BOOLEAN DEFAULT true NOT NULL,
          created_by TEXT,
          updated_by TEXT,
          created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
          updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
        );
      `;

      console.log('✅ workflow_master table created successfully');
    }

    // Create indexes for performance optimization
    console.log('📝 Creating indexes...');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS "workflow_master_name_idx" ON "workflow_master"("name");',
      'CREATE INDEX IF NOT EXISTS "workflow_master_is_active_idx" ON "workflow_master"("is_active");',
      'CREATE INDEX IF NOT EXISTS "workflow_master_created_at_idx" ON "workflow_master"("created_at");',
      'CREATE INDEX IF NOT EXISTS "workflow_master_updated_at_idx" ON "workflow_master"("updated_at");',
    ];

    for (const indexQuery of indexes) {
      await prisma.$executeRaw`${indexQuery}`;
    }

    console.log('✅ Indexes created successfully');

    // Create trigger for updated_at timestamp
    console.log('📝 Creating updated_at trigger...');

    await prisma.$executeRaw`
      CREATE OR REPLACE FUNCTION update_workflow_master_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `;

    await prisma.$executeRaw`
      DROP TRIGGER IF EXISTS update_workflow_master_updated_at ON workflow_master;
      CREATE TRIGGER update_workflow_master_updated_at
        BEFORE UPDATE ON workflow_master
        FOR EACH ROW
        EXECUTE FUNCTION update_workflow_master_updated_at();
    `;

    console.log('✅ Updated_at trigger created successfully');

    // Seed some initial workflow masters
    console.log('📝 Seeding initial workflow masters...');

    const existingWorkflowMasters = await prisma.workflow_master.count();

    if (existingWorkflowMasters === 0) {
      await prisma.workflow_master.createMany({
        data: [
          {
            name: 'Standard Immigration Workflow',
            description: 'Complete workflow template for immigration applications',
            is_active: true,
          },
          {
            name: 'Express Immigration Workflow',
            description: 'Fast-track workflow template for urgent immigration cases',
            is_active: true,
          },
          {
            name: 'Training Program Workflow',
            description: 'Workflow template for training program applications',
            is_active: true,
          },
          {
            name: 'Package Service Workflow',
            description: 'Workflow template for package-based services',
            is_active: true,
          },
          {
            name: 'Consulting Service Workflow',
            description: 'Workflow template for consulting services',
            is_active: true,
          },
        ],
      });

      console.log('✅ Initial workflow masters seeded successfully');
    } else {
      console.log('✅ Workflow masters already exist, skipping seeding');
    }

    console.log('🎉 Workflow Master migration completed successfully!');

    // Display summary
    const totalWorkflowMasters = await prisma.workflow_master.count();
    const activeWorkflowMasters = await prisma.workflow_master.count({
      where: { is_active: true },
    });

    console.log('\n📊 Migration Summary:');
    console.log(`   Total workflow masters: ${totalWorkflowMasters}`);
    console.log(`   Active workflow masters: ${activeWorkflowMasters}`);
    console.log(`   Inactive workflow masters: ${totalWorkflowMasters - activeWorkflowMasters}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateWorkflowMaster()
    .then(() => {
      console.log('✅ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateWorkflowMaster };
