const missingWords = [
  'Managed, Led, Oversaw, Executed, Coordinated',
  'Achieved, Implemented, Developed, Improved',
  'Consistently met deadlines, Worked efficiently under pressure',
  'Collaborated with cross-functional teams, Contributed to team projects',
  '10 years of experience in financial services, Extensive experience in managing large-scale projects',
  'Delivered presentations to executive-level stakeholders, Negotiated contracts resulting in a 15% cost reduction',
  'Increased sales by 30%, Reduced operational costs by 15%',
  'Initiated a new process improvement project that saved 200 hours annually',
  'Created detailed reports ensuring 100% accuracy, Developed error-free documentation',
  'Adapted quickly to changing market conditions, Innovatively approached problem-solving',
  'Developed a strategic plan that increased market share by 10%, Led strategic initiatives that improved customer satisfaction scores',
  'Proactively sought opportunities, Drove key initiatives',
  'Delivered measurable results, Exceeded targets by',
  'Independently managed projects, Initiated key improvements',
  'Expert in, Skilled at',
];

const wordsToAvoid = [
  'Responsible for',
  'Duties included',
  'Hard worker',
  'Team player',
  'Experienced',
  'Excellent communication skills',
  'Results-oriented',
  'Motivated',
  'Detail-oriented',
  'Dynamic',
  'Strategic thinker',
  'Go-getter',
  'Results-driven',
  'Self-motivated',
  'Ninja',
];

const whyToAvoid = [
  'Overused, lacks scope or impact',
  "Generic, doesn't highlight achievements",
  'Subjective, lacks evidence',
  'Overused, vague',
  'Lacks specificity',
  'Broad statement, lacks demonstration',
  "Vague, doesn't showcase actual results",
  'Empty phrase, lacks examples',
  "Common, doesn't show attention to detail",
  'Vague, overused',
  'Broad, lacks illustration',
  'Informal, lacks specificity',
  'Buzzword, lacks specific achievements',
  'Vague, lacks evidence',
  'Informal, unprofessional',
];

export { missingWords, wordsToAvoid, whyToAvoid };
